import {
  Inject,
  //  FORMAT
} from '@midwayjs/core';
import { Processor, IProcessor } from '@midwayjs/bull';
import { HttpService } from '@midwayjs/axios';
import { join } from 'path';
import { RedisService } from '@midwayjs/redis';
import { BaseService } from '@/modules/image/service/base.service.js';
import { ImageService } from '@/modules/image/service/image.service.js';
import {
  IBasePatternPictureFramePageRes,
  IBasePatternGoodsPictureTemplatePageRes,
} from './interface/queue.interface.js';
import { getContoursRectCacheKey } from '@/modules/image/service/utils/cache.js';
import { JSON_TEMP_PATH } from '@/constants/index.js';

@Processor('preLoad', {
  repeat: {
    // cron: FORMAT.CRONTAB.EVERY_DAY_ONE_FIFTEEN,
    cron: '0 0 3 * * *',
  },
})
export class PreLoadProcessor implements IProcessor {
  @Inject() private baseService: BaseService;

  @Inject() private imageService: ImageService;

  @Inject() private redisService: RedisService;

  @Inject() private httpService: HttpService;

  recordDataList: string[] = [];

  async loadFonts() {
    console.log('执行预请求字体任务');
    const result = await this.httpService.request<string[]>({
      method: 'GET',
      url: '/robot-mall-admin/inner/font-config/find-url-all',
    });
    await Promise.all(
      result.data.map(item => this.baseService.downloadFont(item))
    );
    console.log('结束预请求字体任务');
  }

  getContoursRect(url: string) {
    const { cacheKey } = getContoursRectCacheKey(url);
    const tmpJsonFile = join(JSON_TEMP_PATH, `${cacheKey}.json`);

    if (!this.recordDataList.includes(tmpJsonFile)) {
      return this.imageService.getContoursRectWithCache(url);
    }
  }

  async loadFrame() {
    console.log('执行预请求相框任务');
    const result =
      await this.httpService.request<IBasePatternPictureFramePageRes>({
        method: 'POST',
        url: '/robot-mall-admin/inner/v1/base-pattern/picture-frame-page',
        data: {
          pageNum: 1,
          pageSize: 1000,
        },
      });
    await Promise.all(
      result.data.list.map(obj => this.getContoursRect(obj.maskImg))
    );
    console.log('结束预请求相框任务');
  }

  async loadTemplate() {
    console.log('执行预请求蒙层任务');
    const result =
      await this.httpService.request<IBasePatternGoodsPictureTemplatePageRes>({
        method: 'POST',
        url: '/robot-mall-admin/inner/v1/base-pattern/goods-picture-template-page',
        data: {
          pageNum: 1,
          pageSize: 1000,
        },
      });
    let templateList: string[] = [];
    templateList = result.data.list
      .map(item =>
        item.basePatternSource === '0'
          ? [
              item.textureAreaImg.textureAreaImgOneUrl,
              item.textureAreaImg.textureAreaImgTwoUrl,
            ]
          : item.textureAreaInfo.map(obj => obj.textureAreaImgUrl)
      )
      .flat()
      .filter(url => url);
    await Promise.all(templateList.map(item => this.getContoursRect(item)));
    console.log('结束预请求蒙层任务');
  }

  async getRecodeInit() {
    const date = new Date();
    const recordFile = `cleanJonRecord-${date.getFullYear()}/${
      date.getMonth() + 1
    }/${date.getDate()}`;
    let recordDataJSON = await this.redisService.get(recordFile);

    if (recordDataJSON) {
      try {
        this.recordDataList.push(...JSON.parse(recordDataJSON));
      } catch (error) {}
    }
  }

  async execute() {
    await this.getRecodeInit();

    this.loadFonts();
    this.loadFrame();
    this.loadTemplate();
  }
}
