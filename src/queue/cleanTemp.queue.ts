import fs from 'fs-extra';
import { join } from 'node:path';
import { Inject, FORMAT } from '@midwayjs/core';
import { RedisService } from '@midwayjs/redis';
import { Processor, IProcessor } from '@midwayjs/bull';
import { LockfileService } from '@/modules/lockfile/service/lockfile.service.js';
import { IMAGES_TEMP_PATH, JSON_TEMP_PATH } from '@/constants/index.js';

@Processor('cleanTemp', {
  repeat: {
    cron: FORMAT.CRONTAB.EVERY_DAY_ONE_FIFTEEN,
  },
})
export class CleanTempProcessor implements IProcessor {
  @Inject() private redisService: RedisService;

  @Inject() private lockfileService: LockfileService;

  async cleanImages() {
    console.log('执行清理图片缓存任务');

    // const oneMonthAgo = new Date();
    // oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    const now = new Date();
    const twoWeeksAgo = new Date(now.getTime() - 2 * 7 * 24 * 60 * 60 * 1000);
    const files = await fs.readdir(IMAGES_TEMP_PATH);

    for (const file of files) {
      const filePath = join(IMAGES_TEMP_PATH, file);
      const stats = await fs.stat(filePath);
      if (twoWeeksAgo.getTime() > stats.atimeMs) {
        await fs.unlink(filePath);
      }
    }

    console.log('结束清理图片缓存任务');
  }

  async cleanJson() {
    console.log('执行清理json缓存任务');
    const date = new Date();
    const recordFile = `cleanJonRecord-${date.getFullYear()}/${
      date.getMonth() + 1
    }/${date.getDate()}`;
    const recordDataList: string[] = [];
    let recordDataJSON = await this.redisService.get(recordFile);

    if (recordDataJSON) {
      try {
        recordDataList.push(...JSON.parse(recordDataJSON));
      } catch (error) {}
    }

    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    const files = await fs.readdir(JSON_TEMP_PATH);

    for (const file of files) {
      const filePath = join(JSON_TEMP_PATH, file);
      const stats = await fs.stat(filePath);

      if (oneMonthAgo.getTime() > stats.atimeMs) {
        recordDataList.push(filePath);
        this.lockfileService.lockFile(() => {
          return fs.unlink(filePath);
        }, filePath);
      }
    }
    await this.redisService.set(
      recordFile,
      JSON.stringify(recordDataList),
      'EX',
      1 * 60 * 60 * 12
    );
    console.log('结束清理json缓存任务');
  }

  async execute() {
    this.cleanImages();
    this.cleanJson();
  }
}
