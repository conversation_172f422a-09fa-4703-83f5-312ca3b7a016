// ⬇️ 内部查询相框列表（分页）请求体 接口：https://yapi.textile-story.com/project/1208/interface/api/85270
/**
 * 分页对象
 */
export interface IBasePatternPictureFramePageReq {
  pageNum?: number;
  pageSize?: number;
}
// ⬆️ 内部查询相框列表（分页）请求体

// ⬇️ 内部查询相框列表（分页）响应体 接口：https://yapi.textile-story.com/project/1208/interface/api/85270
export interface IBasePatternPictureFramePageRes {
  page: string;
  total: string;
  list: IBasePatternPictureFramePageResListItem[];
}
export interface IBasePatternPictureFramePageResListItem {
  /**
   * 相框id
   */
  pictureFrameId: string;
  /**
   * 原始图片
   */
  pictureImg: string;
  /**
   * 相框图片
   */
  pictureFrameImg: string;
  /**
   * 蒙版图片
   */
  maskImg: string;
  /**
   * 排序
   */
  sort: string;
  /**
   * 状态(未发布:0,发布:1)
   */
  state: string;
  /**
   * 创建人id
   */
  creatorId: string;
  /**
   * 创建人姓名
   */
  creatorName: string;
  /**
   * 创建时间
   */
  createdTime: string;
  /**
   * 修改人id
   */
  reviserId: string;
  /**
   * 修改人姓名
   */
  reviserName: string;
  /**
   * 修改时间
   */
  revisedTime: string;
  /**
   * 是否删除：0否 1 是
   */
  deleted: string;
}
// ⬆️ 内部查询相框列表（分页）响应体

// ⬇️ 内部查询图套信息请求体 接口：https://yapi.textile-story.com/project/1208/interface/api/85266
export interface IBasePatternGoodsPictureTemplatePageReq {
  pageNum?: number;
  pageSize?: number;
}
// ⬆️ 内部查询图套信息请求体

// ⬇️ 内部查询图套信息响应体 接口：https://yapi.textile-story.com/project/1208/interface/api/85266
export interface IBasePatternGoodsPictureTemplatePageRes {
  page: string;
  total: string;
  list: IBasePatternGoodsPictureTemplatePageResListItem[];
}
export interface IBasePatternGoodsPictureTemplatePageResListItem {
  /**
   * 版型来源 0:robot 1:plm 2:buyer
   */
  basePatternSource: string;
  /**
   * 商品图套id
   */
  goodsPictureTemplateId: string;
  /**
   * 商品id
   */
  goodsId: string;
  /**
   * 商品图套名称
   */
  goodsPictureTemplateName: string;
  /**
   * 角度(1 正面)
   */
  angle: string;
  textureAreaImg: IBasePatternGoodsPictureTemplatePageResTextureAreaImg;
  /**
   * 贴图区域列表
   */
  textureAreaInfo: IBasePatternGoodsPictureTemplatePageResTextureAreaInfoItem[];
  /**
   * 颜色图片(json格式[{颜色id：，颜色code：，颜色名称：，颜色图片：}])
   */
  colorImg: IBasePatternGoodsPictureTemplatePageResColorImgItem[];
}
/**
 * 贴图区域
 */
export interface IBasePatternGoodsPictureTemplatePageResTextureAreaImg {
  /**
   * 贴图区域小于1.5图片url
   */
  textureAreaImgOneUrl: string;
  textureAreaImgOneCoordinates: IBasePatternGoodsPictureTemplatePageResTextureAreaImgOneCoordinates;
  /**
   * 贴图区域大于1.5图片url
   */
  textureAreaImgTwoUrl: string;
  textureAreaImgTwoCoordinates: IBasePatternGoodsPictureTemplatePageResTextureAreaImgTwoCoordinates;
}
/**
 * 贴图区域小于1.5图片坐标
 */
export interface IBasePatternGoodsPictureTemplatePageResTextureAreaImgOneCoordinates {
  width: string;
  height: string;
  x: string;
  y: string;
}
/**
 * 贴图区域大于1.5图片坐标
 */
export interface IBasePatternGoodsPictureTemplatePageResTextureAreaImgTwoCoordinates {
  width: string;
  height: string;
  x: string;
  y: string;
}
export interface IBasePatternGoodsPictureTemplatePageResTextureAreaInfoItem {
  /**
   * 区域下标
   */
  areaIndex: string;
  /**
   * 链接
   */
  textureAreaImgUrl: string;
}
export interface IBasePatternGoodsPictureTemplatePageResColorImgItem {
  /**
   * 颜色编码
   */
  colorCode: string;
  /**
   * 颜色名称
   */
  colorName: string;
  /**
   * 颜色对应的图片
   */
  colorUrl: string;
}
// ⬆️ 内部查询图套信息响应体
