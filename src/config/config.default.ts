import { MidwayConfig } from '@midwayjs/core';
import { REDIS_ENV } from '@/constants/index.js';

export default {
  // use for cookie sign key, should change to your own and keep security
  keys: '1732696063577_6963',
  koa: {
    port: 7001,
    // globalPrefix: '/api',
  },
  // 静态文件配置
  staticFile: {
    dirs: {
      public: {
        prefix: '/', // URL 前缀，例如：`/static`，设置为空表示直接根路径
        dir: 'public', // 指定文件夹，相对项目根路径
      },
    },
  },
  redis: {
    client: REDIS_ENV[process.env.API_ENV || 'dev1'],
  },
  security: {
    csrf: {
      enable: false,
    },
    xssProtection: {
      enable: true,
      value: 'block',
    },
    nosniff: {
      enable: true,
    },
    xframe: {
      enable: true,
      // 只允许来自相同域名的嵌套
      value: 'SAMEORIGIN',
    },
  },
  cache: {
    options: {
      max: 500,
      ttl: 2 * 60 * 60,
    },
  },
  midwayLogger: {
    default: {
      level: 'info',
      transports: {
        console: {
          level: 'info',
        },
      },
    },
  },
  oss: {
    client: {
      accessKeyId: 'LTAI5tR9TTSHVdBWyXboSHc1',
      accessKeySecret: '******************************',
      bucket: 'cloud-chuangxin',
      endpoint: 'oss-accelerate.aliyuncs.com',
      secure: true,
      timeout: '60s',
    },
  },
  bull: {
    defaultQueueOptions: {
      redis: REDIS_ENV[process.env.API_ENV || 'dev1'],
    },
  },
  cors: {
    origin: '*',
  },
} as MidwayConfig;
