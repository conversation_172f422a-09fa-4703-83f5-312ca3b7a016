import { Inject, Middleware, IMiddleware, httpError } from '@midwayjs/core';
import { RedisService } from '@midwayjs/redis';
import { RateLimiterRedis } from 'rate-limiter-flexible';
import { NextFunction, Context } from '@midwayjs/koa';
import { SYSTEM_CODE } from '@/constants/index.js';

@Middleware()
export class RateLimiterMiddleware
  implements IMiddleware<Context, NextFunction>
{
  @Inject()
  redisService: RedisService;

  rateLimiter: RateLimiterRedis;

  resolve() {
    if (!this.rateLimiter) {
      this.rateLimiter = new RateLimiterRedis({
        storeClient: this.redisService,
        keyPrefix: `${SYSTEM_CODE}-rateLimiter`,
        points: 15,
        duration: 1,
      });
    }
    return async (ctx: Context, next: NextFunction) => {
      try {
        await this.rateLimiter.consume(`${ctx.ip}_${ctx.path}`);
      } catch (e) {
        ctx.logger.error(e);
        const error = new httpError.BadGatewayError();
        error.code = 429;
        error.message = '请求太频繁，请稍后再试';

        try {
          error.message += ' - ' + JSON.stringify(e);
        } catch ($e) {}
        throw error;
      }
      return next();
    };
  }

  static getName(): string {
    return 'rateLimiterMiddleware';
  }
}
