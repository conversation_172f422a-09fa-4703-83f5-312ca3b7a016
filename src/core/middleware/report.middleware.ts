import { Middleware, IMiddleware } from '@midwayjs/core';
import { NextFunction, Context } from '@midwayjs/koa';

@Middleware()
export class ReportMiddleware implements IMiddleware<Context, NextFunction> {
  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      // 控制器前执行的逻辑
      const startTime = Date.now();
      // 执行下一个 Web 中间件，最后执行到控制器
      // 这里可以拿到下一个中间件或者控制器的返回值
      const result = await next();
      // 控制器之后执行的逻辑
      ctx.logger.info('-----------------------------------------------');
      ctx.logger.info(`Report rt = ${Date.now() - startTime}ms`);
      if (ctx.request.header.userinfo) {
        try {
          ctx.logger.info(
            '用户信息 --- ',
            JSON.stringify(
              JSON.parse(ctx.request.header.userinfo as string),
              null,
              2
            )
          );
        } catch (e) {
          ctx.logger.info('用户信息 --- ', ctx.request.header.userinfo);
        }
      }
      if (ctx.request.header.deviceinfo) {
        try {
          ctx.logger.info(
            '设备信息 --- ',
            JSON.stringify(
              JSON.parse(ctx.request.header.deviceinfo as string),
              null,
              2
            )
          );
        } catch (e) {
          ctx.logger.info('设备信息 --- ', ctx.request.header.deviceinfo);
        }
      }

      ctx.logger.info(
        `Report request = ${ctx.request.method} ${
          ctx.request.URL
        } ${JSON.stringify(ctx.request.body, null, 2)}`
      );
      ctx.logger.info(
        `Report response = ${JSON.stringify(ctx.response.body, null, 2)}`
      );
      ctx.logger.info(
        '-----------------------------------------------',
        '\n\n'
      );
      // 返回给上一个中间件的结果
      return result;
    };
  }

  static getName(): string {
    return 'report';
  }
}
