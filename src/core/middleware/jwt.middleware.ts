import { Inject, Middleware, httpError } from '@midwayjs/core';
import { Context, NextFunction } from '@midwayjs/koa';
import { JwtService } from '@midwayjs/jwt';
import {
  weappPublicKey,
  ssoDevQaPublicKey,
  ssoUatBlueProdPublicKey,
} from '@/constants/index.js';

@Middleware()
export class JwtMiddleware {
  @Inject()
  jwtService: JwtService;

  public static getName(): string {
    return 'jwt';
  }

  decodeBase64PublicKey(rawBase64: string): string {
    return `-----BEGIN PUBLIC KEY-----\n${rawBase64
      .match(/.{1,64}/g)
      .join('\n')}\n-----END PUBLIC KEY-----`;
  }

  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      // 本地开发跳过校验
      if (process.env.NODE_ENV === 'local') {
        return await next();
      }
      // 判断下有没有校验信息
      if (!ctx.headers['token'] && !ctx.headers['ssotoken']) {
        throw new httpError.UnauthorizedError();
      }
      let base64PublicKey = '';
      if (ctx.get('System-Code') === 'SCF_ROBOT_SEW') {
        base64PublicKey = weappPublicKey;
      } else if (
        process.env.API_ENV === 'dev1' ||
        process.env.API_ENV === 'qa1'
      ) {
        base64PublicKey = ssoDevQaPublicKey;
      } else if (
        process.env.API_ENV === 'uat1' ||
        process.env.API_ENV === 'blue' ||
        process.env.API_ENV === 'prod'
      ) {
        base64PublicKey = ssoUatBlueProdPublicKey;
      }
      try {
        // 加载公钥
        const publicKey = this.decodeBase64PublicKey(base64PublicKey);
        await this.jwtService.verify(
          ctx.get('token') || ctx.get('ssotoken'),
          publicKey,
          {
            algorithms: ['RS256'],
          }
        );
      } catch (error) {
        throw new httpError.UnauthorizedError();
      }
      await next();
    };
  }

  // 配置忽略鉴权的路由地址
  public match(ctx: Context): boolean {
    const ignore = ctx.path.indexOf('/api/admin/login') !== -1;
    return !ignore;
  }
}
