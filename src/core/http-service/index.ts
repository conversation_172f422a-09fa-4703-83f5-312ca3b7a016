import { IMidwayContainer } from '@midwayjs/core';
import * as axios from '@midwayjs/axios';
import { join } from 'node:path';

export class HttpService {
  get baseHostName() {
    return 'scf-tg-api.textile-story.com' as const;
  }
  get systemDomainMap() {
    return {
      dev1: `https://dev1-${this.baseHostName}`,
      qa1: `https://qa1-${this.baseHostName}`,
      uat1: `https://uat1-${this.baseHostName}`,
      blue: `https://blue-${this.baseHostName}`,
      prod: `https://${this.baseHostName}`,
    } as const;
  }
  handleRequestConfigUrl(config: axios.Axios.InternalAxiosRequestConfig) {
    const isExternal = /^(https?:)/.test(config.url!);
    if (!isExternal) {
      config.url = join(
        this.systemDomainMap[process.env.API_ENV || 'dev1'],
        config.url
      );
    }
    return config;
  }

  /**
   * 处理后端接口相关的响应数据
   */
  handleResponseSuccess(config: axios.Axios.AxiosResponse) {
    if (
      config.config.url.includes(this.baseHostName) &&
      config.data &&
      typeof config.data === 'object'
    ) {
      const res = config.data;
      return res;
    }
    return config;
  }
  async setInterceptors(container: IMidwayContainer) {
    const httpService = await container.getAsync(axios.HttpService);
    httpService.interceptors.response.use(config =>
      this.handleResponseSuccess(config)
    );
    httpService.interceptors.request.use(config =>
      this.handleRequestConfigUrl(config)
    );
  }
}
