export const controllerResponse = <T>(data: T) => {
  return {
    code: '200',
    message: '',
    successful: true,
    data,
  };
};

/**
 * 原始字符串转短字符串
 */
export const generateShortKey = (longString: string) => {
  let hash = 0;
  for (let i = 0; i < longString.length; i++) {
    const char = longString.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash |= 0;
  }
  const shortKey = Math.abs(hash).toString(36).slice(0, 6).toUpperCase();
  return shortKey;
};

export const toUtf8CStyle = (input: string) => {
  return Array.from(input)
    .map(char => {
      const code = char.charCodeAt(0);
      // ASCII 范围的字符直接保留
      if (code <= 0x7f) return char;
      // 非 ASCII 字符转为 UTF-8 转义格式
      return Buffer.from(char, 'utf-8').reduce(
        (acc, byte) => acc + `\\x${byte.toString(16).padStart(2, '0')}`,
        ''
      );
    })
    .join('');
};
