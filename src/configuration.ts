import { Configuration, App, IMidway<PERSON>ontainer, Inject } from '@midwayjs/core';
import * as koa from '@midwayjs/koa';
import * as validate from '@midwayjs/validate';
import * as info from '@midwayjs/info';
import * as axios from '@midwayjs/axios';
import * as staticFile from '@midwayjs/static-file';
import * as redis from '@midwayjs/redis';
import * as Security from '@midwayjs/security';
import * as jwt from '@midwayjs/jwt';
import * as cache from '@midwayjs/cache';
import * as oss from '@midwayjs/oss';
import * as bull from '@midwayjs/bull';
import * as bullBoard from '@midwayjs/bull-board';
import * as crossDomain from '@midwayjs/cross-domain';
import fs from 'fs-extra';
import sharp from 'sharp';
import { DefaultErrorFilter } from './core/filter/default.filter.js';
import { NotFoundFilter } from './core/filter/notfound.filter.js';
import { ReportMiddleware } from './core/middleware/report.middleware.js';
import { RateLimiterMiddleware } from './core/middleware/ratelimiter.middleware.js';
import { JwtMiddleware } from './core/middleware/jwt.middleware.js';
import DefaultConfig from './config/config.default.js';
import UnittestConfig from './config/config.unittest.js';
import type { LockfileService } from './modules/lockfile/service/lockfile.service.js';
import {
  IMAGES_TEMP_PATH,
  FONTS_TEMP_PATH,
  JSON_TEMP_PATH,
} from './constants/index.js';
import { HttpService } from './core/http-service/index.js';
// 要创建的目录
const MKDIR_DIR_LIST = [FONTS_TEMP_PATH, IMAGES_TEMP_PATH, JSON_TEMP_PATH];

@Configuration({
  imports: [
    koa,
    validate,
    {
      component: info,
      enabledEnvironment: ['local'],
    },
    axios,
    staticFile,
    redis,
    Security,
    jwt,
    cache,
    oss,
    bull,
    bullBoard,
    crossDomain,
  ],
  importConfigs: [
    {
      default: DefaultConfig,
      unittest: UnittestConfig,
    },
  ],
})
export class MainConfiguration extends HttpService {
  @App('koa')
  app: koa.Application;

  lockfileService: LockfileService;

  @Inject()
  bullFramework: bull.Framework;

  async mkdirDir() {
    for await (const dir of MKDIR_DIR_LIST) {
      if (!(await fs.exists(dir))) {
        try {
          this.lockfileService.lockFile(
            () => fs.mkdir(dir, { recursive: true }),
            dir
          );
        } catch (error) {
          console.error(error);
        }
      }
    }
  }

  async onReady(container: IMidwayContainer) {
    const appContext = this.app.getApplicationContext();
    this.lockfileService = await appContext.getAsync('lockfileService');

    this.setInterceptors(container);
    // add middleware
    this.app.useMiddleware([
      JwtMiddleware,
      ReportMiddleware,
      RateLimiterMiddleware,
    ]);
    // add filter
    this.app.useFilter([NotFoundFilter, DefaultErrorFilter]);
    sharp.cache({
      memory: 500,
      files: 50,
    });
    await this.mkdirDir();
  }
}
