import process from 'node:process';
import { join } from 'node:path';

const DATA_DIR =
  process.env.NODE_ENV === 'production'
    ? '/data/robot-sew-node-service/'
    : process.cwd();

/**
 * 文字临时文件
 *
 * 字体文件先不放在数据盘，文字经常出现乱码
 */
export const FONTS_TEMP_PATH = join(process.cwd(), '/public/temp/fonts');
/** 图片临时文件 */
export const IMAGES_TEMP_PATH = join(DATA_DIR, '/public/temp/images');
/** JSON 临时文件 */
export const JSON_TEMP_PATH = join(DATA_DIR, '/public/temp/json');

export const ASSETS_FONTS_PATH = join(process.cwd(), 'src/assets/fonts');

/** 自定义系统 code */
export const SYSTEM_CODE = 'ROBOT_SEW_NODE_SERVICE';
/** cache key */
export const CACHE_KEY = {
  /** 轮廓矩形信息 */
  CONTOURS_RECT: `${SYSTEM_CODE}-CONTOURS_RECT`,
  /** 带背景色的图片（现在是相框图） */
  IMAGE_WITH_BG: `${SYSTEM_CODE}-MASK_URL_WITH_BG`,
  /** 款式图 */
  STYLE_IMAGE: `${SYSTEM_CODE}-STYLE_IMAGE`,
  /** 根据蒙层轮廓去除图片对应位置 */
  SOURCE_IMAGE_WITH_OUT_CONTOURS: `${SYSTEM_CODE}-SOURCE_IMAGE_WITH_OUT_CONTOURS`,
  /** 调整蒙层尺寸 */
  MASK_URL_SIZE_RESIZE: `${SYSTEM_CODE}-MASK_URL_SIZE_RESIZE`,
} as const;

/** 喜T小程序公钥 */
export const weappPublicKey =
  'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAm1RHov62rLtxJZBYUt8T3iIZxsU75UxwLAlaIXIp92yJiCFv/yY1y2lSQZSHN5Kx/rvcqUa/7455Ik+il7+LNFpmHCjfkFoocGSAhI/2OzI8lP+qXNFrRXt8CgOZKj6chcTkytcObP/3RGvoTTkWV2zThuj5biZLPbHEalKEf8zFDQEXDEj7uEbkPBMw35wQqwqklcxR1gUCTbYZmt7sULRofzU5lQ4+D/DOWbRkQiPvi5Z606aHx/oKBHMufcI4/0CtLKjLCp7Zm5UVDnj1dbfgvtRl6ZEHyW8jLbEIgnuCVnXbHqCp1QgeBuc+/71vmP8a2RaohS44zxpWcaRTSQIDAQAB';

/** sso系统dev、qa环境公钥 */
export const ssoDevQaPublicKey =
  'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA6RDHlPg/v37w0gjhbYCjsLgds6wDHzTAZ29j1CrEZRQb75kw0jJdCAovwAbqI4JpJWGLYiKph2WCAg4BY9wwvdNlisQT8UmmmlUJNfxSMe4XjC6PaCcjngqlRi9kzioB74GYVWvtsreuD1a3HAfFEVd8UWxqfoyjdboEFHVqNFdsYyCndz7HufYDu/r2xlLWPkMBHXP27g+kzEPYYmUEaXm9GEqQAgSIg8msHfgugu+ZiNGyy4+p4s5axmLF2p+p3kdd0IBKDmwqfgWCADLzWU1AUU7yzwHj6a8syE0s7jZlXyXszsTRHv6vPZszhKVlZIsUf2L+uvJW7JdiazsjowIDAQAB';
// 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtGScrVA4EXGklclyiJLsT9qwjCf0nSAjl6zi/lcJ0AXwSXPhpyd7qZrpPqvFvnTAOIdpqAyqgjGdgg0y24keWxgmxkpULHqmKsiN7yLzV7evt3UUhyoplMi3mNd4ck85LCCNHfY+gZ2IMOVQlwd1mkBcNNIAL0wp3Vw3AMtPq4MTyXpRLHd5LKwL4/P3rFdZw4EysZFFlAHA5f0jgVVgeZlLmmOSVGvubekFpRC1sY4dMP9cl57EPegtjxxHcrLwN4/8PtliLUP+PbChO0k8Memz3luL76Ya+tIDGvoGIQAN1jGswBkiweXuYzrxYDDnjbE4zi+R+jkfI3/x2AEKEwIDAQAB';

/** sso系统uat、prod环境公钥 */
export const ssoUatBlueProdPublicKey =
  'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgjParPmslm+bBSGKmnFb46RvMPoTBOYemEW/Tm7T9yPKKqSHrEKXwHbnGdQRtW/qzIqZUbGDcVca007jZKjbBnrrboxZ8o2K7uGeT70H/EZIKtprLXH/vDzN5/87uYj8+h1m1mg5abRoMn4ChMPzjVUhbB72tAAUxcRfbKkFZv6nGSwBEtDgeb8UuRsPshv5kBy+i5hURwnKp+T1djJP0QDd9WfmPr9TMZ08kKZgSAYWTNVYStWTkU7nk1LW6HtCEBtL5PE4rZNcp7nYDKadYH7F7TARYHCBSQUSTy1z0+68Az27eT7QdjhwuzDYXg5UBPCZ0tpeb7voefucbxMzHQIDAQAB';

// redis因与后端共用，前端统一使用 db5 来区分
export const REDIS_ENV = {
  dev1: {
    /* port: 6379,
    host: 'r-bp11bu3oq0erj4uvyv.redis.rds.aliyuncs.com',
    password: '8bcGDgxV6lQEhI#rz8g1jEzi', */
    host: 'r-bp1c2yukt8vxopu4p5.redis.rds.aliyuncs.com',
    port: 6379,
    password: 'JuVL6CNGFbplQ1qMTEjb',
    db: 5,
  },
  qa1: {
    /* port: 6379,
    host: 'r-bp16dqxbsblkg26mm0.redis.rds.aliyuncs.com',
    password: '8bcGDgxV6lQEhI#rz8g1jEzi', */
    host: 'r-bp18091u62z3yl90cg.redis.rds.aliyuncs.com',
    port: 6379,
    password: 'JuVL6CNGFbplQ1qMTEjb',
    db: 5,
  },
  uat1: {
    port: 6379,
    host: 'r-bp1hzes3o9p3mukbm6.redis.rds.aliyuncs.com',
    password: 'bg8TjsqfaNIAxTjStlx7glEbLGxumy',
    db: 5,
  },
  blue: {
    /* port: 6379,
    host: 'r-bp1usvl7d2en5y9epo.redis.rds.aliyuncs.com',
    password: 'NDmUnP8c1rY4BQe5', */
    host: 'r-bp1zn7654ql05xjqf7.redis.rds.aliyuncs.com',
    port: 6379,
    password: 'EFJNXRCUkJwdVVtd0hM',
    db: 5,
  },
  prod: {
    /* port: 6379,
    host: 'r-bp1usvl7d2en5y9epo.redis.rds.aliyuncs.com',
    password: 'NDmUnP8c1rY4BQe5', */
    host: 'r-bp1zn7654ql05xjqf7.redis.rds.aliyuncs.com',
    port: 6379,
    password: 'EFJNXRCUkJwdVVtd0hM',
    db: 5,
  },
};
