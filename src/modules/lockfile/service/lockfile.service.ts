import { Provide, Inject } from '@midwayjs/core';
import { RedisService } from '@midwayjs/redis';
import { AnyFunction } from '../interface/lockfile.interface.js';

@Provide()
export class LockfileService {
  @Inject() private redisService: RedisService;
  /** 锁文件前缀 */
  lockFileKeyPrefix = 'lock-key:';
  ttl = 200;

  private getLockKey(key: string) {
    return `${this.lockFileKeyPrefix}${key}`;
  }

  private async setLock(key: string, ttl = this.ttl) {
    const result = await this.redisService.set(
      this.getLockKey(key),
      key,
      'EX',
      ttl
    );
    return result === 'OK';
  }

  private async delLock(key: string) {
    const result = await this.redisService.del(this.getLockKey(key));
    return result === 1;
  }

  private getLockKeyInRedis(key: string) {
    return this.redisService.get(this.getLockKey(key));
  }

  async lockFile(callback: AnyFunction, key: string, ttl = this.ttl) {
    const lockKey = await this.getLockKeyInRedis(key);
    const invoker = async () => {
      try {
        await callback();
      } finally {
        const result = await this.delLock(key);
        if (result) {
          console.log(`锁已清除 - ${lockKey}`);
        } else {
          console.error(`锁清除失败 - ${lockKey}`);
        }
      }
    };

    if (lockKey) {
      console.log('已存在相同文件，锁定中，跳过写文件');
    } else {
      const lock = await this.setLock(key, ttl);
      if (lock) {
        await invoker();
      }
    }
  }
}
