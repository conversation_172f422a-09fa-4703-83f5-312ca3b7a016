import { Inject, Provide } from '@midwayjs/core';
import { OSSService } from '@midwayjs/oss';
import { consoleTime } from '@/core/utils/console-time.js';
import { OssImageInfo } from '@/modules/image/interface/image.interface.js';

@Provide()
export class OssService {
  @Inject() private ossService: OSSService;

  async uploadFile(filename: string, file: Buffer): Promise<string> {
    const timeEnd = consoleTime('oss上传文件');
    const result = await this.ossService.put(filename, file);
    timeEnd();
    return result.url.replace(
      'cloud-chuangxin.oss-accelerate.aliyuncs.com',
      'oss.yunbanfang.cn'
    );
  }

  async downloadFile(filename: string, process?: string): Promise<ArrayBuffer> {
    const timeEnd = consoleTime('oss下载文件');
    const result = await this.ossService.get(filename, {
      process,
    });
    timeEnd();
    return result.content;
  }

  async getImageInfo(filename: string): Promise<OssImageInfo> {
    const timeEnd = consoleTime('图片元信息获取耗时');
    const result = await this.ossService.get(filename, {
      process: 'image/info',
    });
    timeEnd();
    return JSON.parse(result.content.toString());
  }
}
