import sharp from 'sharp';
/**
 * 元素位置信息
 */
export interface ElementRect {
  /**
   * 元素图左上角的 x 坐标
   *
   * 注意，非元素图内容的 x 坐标
   */
  x: number;
  /**
   * 元素图左上角的 y 坐标
   *
   * 注意，非元素图内容的 x 坐标
   */
  y: number;
  /** 元素图的宽高 */
  width: number;
  /** 元素图的宽高 */
  height: number;
  /**
   * 放大值 最终缩放值是 scale * ratio
   *
   * x y width height 等都会受 最终缩放值 影响
   *
   * @default 1
   */
  scale?: number;
  /**
   * 缩放值 最终缩放值是 scale * ratio
   *
   * x y width height 等都会受 最终缩放值 影响
   *
   * @default 1
   */
  ratio?: number;
  /**
   * 旋转角度
   */
  rotate?: number;
  /**
   * 最大宽度
   */
  maxWidth?: number;
}

export type CreateElementImageFnOpts = {
  /** 内容，暂时只有文字 */
  content: string;
  /** 元素信息 */
  elementRect: ElementRect;
  /** 元素样式 */
  styleData: Record<string, string | number>;
  /**
   * 文字相关
   */
  font?: {
    /** 字体 url */
    path: string;
    /**
     * 字体名称
     */
    family: string;
  };
};

export interface CreateDecorativeImageWithMaskOpts {
  url: string;
  maskUrl: string;
  color: string;
}

export interface CreateDecorativeImageWithMaskCacheData {
  url: string;
  width: number;
  height: number;
}

export type ClearTrajectoryPathOpts = Omit<
  CreateDecorativeImageWithMaskOpts,
  'color'
>;

/**
 * OSS 图片信息
 */
export type OssImageInfo = {
  FileSize: { value: string };
  Format: { value: string };
  FrameCount: { value: string };
  ImageHeight: { value: string };
  ImageWidth: { value: string };
};

export interface Point {
  x: number;
  y: number;
}

export type TrajectoryLine = { x: number; y: number; width: number }[][];

export interface ContoursRectData {
  trajectoryLine: TrajectoryLine;
  peripheralTrajectoryLine: TrajectoryLine;
  /** 轮廓轨迹 */
  // trajectory: Point[];
  /** 外部轨迹 */
  // peripheralTrajectory: Point[];
  /** 轮廓宽度 */
  contourWidth: number;
  /** 轮廓高度 */
  contourHeight: number;
  /** 轮廓总像素 */
  // whitePixelCount: number;
  /** 轮廓开始点 x */
  x: number;
  /** 轮廓开始点 y */
  y: number;
  /** 轮廓结束点 x */
  // maxX: number;
  /** 轮廓结束点 y */
  // maxY: number;
  /** 图片宽度 */
  imageWidth: number;
  /** 图片高度 */
  imageHeight: number;
}

export interface CalculateContoursOpts {
  raw: Buffer;
  width: number;
  height: number;
}

export interface ToFormatOpts {
  url: string;
  format: keyof sharp.FormatEnum | sharp.AvailableFormatInfo;
}

export interface ImageExtract extends Partial<Omit<ToFormatOpts, 'url'>> {
  url: string | Buffer;
  rect: sharp.Region;
  /**
   * 针对 `.image` 自动转换格式，默认截取 x-oss-process/image/format 的字段，没有则是 jpg
   * 
   * 如果定义了 `format` 则使用format的参数
   * 
   * @default true
   */
  imageFileTypeAutoToFormat?: boolean;
}

export interface ContextRect {
  /** 画布大小 宽高 */
  width: number;
  /** 画布大小 宽高 */
  height: number;
}
/**
 * 底图信息
 */
export interface ImageSynthesisSource {
  /**
   * 底板图 url
   *
   * 如果不传，那必须传 {@linkcode ImageSynthesisSource.contextRect}
   */
  source?: string;
  /**
   * 底板 蒙层图 url
   * 用于计算绘制区轨迹
   */
  sourceMask?: string;
  /**
   * source url 的 x 坐标
   *
   * @default 0
   */
  x?: number;
  /**
   * source url 的 y 坐标
   * @default 0
   */
  y?: number;
  /**
   * 画布信息
   *
   * 如不设置则是 source 的尺寸
   */
  contextRect?: ContextRect;
  /** 如有 缩放值 则所有的大小都会计算 */
  zoom?: Pick<ElementRect, 'scale' | 'ratio'>;
  /**
   * 相框图宽高 不传则是图片原始尺寸
   */
  width?: number;
  /**
   * 相框图宽高 不传则是图片原始尺寸
   */
  height?: number;
}
/**
 * 元素图信息
 */
export interface ImageSynthesisPattern {
  /** 元素图 url */
  pattern: string;
  /**
   * 偏移值 x。
   * 由于 图片会转换为{@linkcode ImageSynthesisSource.sourceMask}轨迹的大小（等比压缩）
   * 该参数指定为在轨迹中开始绘制的位置，默认为左右居中（自动计算）
   */
  offsetX?: number;
  /**
   * 偏移值 y。
   * 由于 图片会转换为{@linkcode ImageSynthesisSource.sourceMask}轨迹的大小（等比压缩）
   * 该参数指定为在轨迹中开始绘制的位置，默认为左右居中（自动计算）
   */
  offsetY?: number;
  /**
   * 元素图宽高 不传则是图片原始尺寸
   */
  width?: number;
  /**
   * 元素图宽高 不传则是图片原始尺寸
   */
  height?: number;
  /** 如有 缩放值 则所有的大小都会计算 */
  zoom?: Pick<ElementRect, 'scale' | 'ratio'>;
  /**
   * 1216 需求增加
   *
   * 旋转角度
   */
  rotate?: number;
}

export interface ImageSynthesisRet {
  url: string;
  contourPattern: string;
  width: number;
  height: number;
}

export interface ImageSynthesisFnOpts {
  /** 底图（相框）信息 */
  source: ImageSynthesisSource;
  /** 元素图信息 */
  pattern: ImageSynthesisPattern;
  other?: {
    /** 绘制其他 文字等元素 */
    elements?: CreateElementImageFnOpts[];
    /**
     * 文件保存格式
     */
    fileType?: ImageFileType;
    /**
     * 绘制底图的时机，前置还是后置
     *
     * 业务上：
     * 如果是 相框+元素 使用 `suffix`
     * 如果是 底图+元素 使用 `prefix`
     *
     * @default 'suffix'
     */
    drawSourceOccasion?: 'prefix' | 'suffix';
    /**
     * 用于缓存 `Redis` 的 `key`
     *
     * 如果没有缓存的需求不建议使用，适合已生成的款式图场景
     */
    catchKey?: string;
    /**
     * 是否获取元素图贴在轮廓中的图片（只有轮廓部分）
     */
    getContourPattern?: boolean;
  };
}
/** 对应 node-canvas 图片格式 */
export type ImageFileType = 'png' | 'jpeg';
export type ZoomValue = Pick<ElementRect, 'scale' | 'ratio'>;

/**
 * 蒙层图信息
 */
export interface ImageSynthesisContourMask {
  url: string;
  width: number;
  height: number;
}

export interface ImageSynthesisContourPattern {
  url: string;
  width?: number;
  height?: number;
  x?: number;
  y?: number;
  zoom?: ZoomValue;
  rotate?: number;
}

export interface GetContoursRectDataOpts {
  maskUrl: string;
}

export interface CreateContourImageOpts {
  maskData: {
    url: string;
    width?: number;
    height?: number;
  };
  pattern: ImageSynthesisContourPattern;
}

export interface UrlSizeResizeOpts {
  url: string;
  maskUrl: string;
}

export interface UrlSizeResizeRet {
  url: string;
  maskUrl: string;
  width: number;
  height: number;
}
