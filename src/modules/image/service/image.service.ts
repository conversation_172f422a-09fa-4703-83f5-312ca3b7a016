import { Provide, Inject, httpError } from '@midwayjs/core';
import sharp from 'sharp';
import { join } from 'node:path';
import { RedisService } from '@midwayjs/redis';
import { registerFont, createCanvas, loadImage, Image } from 'canvas';
import { CacheManager } from '@midwayjs/cache';
import fs from 'fs-extra';
import {
  CreateElementImageFnOpts,
  CreateDecorativeImageWithMaskOpts,
  CreateDecorativeImageWithMaskCacheData,
  ImageSynthesisFnOpts,
  ZoomValue,
  ImageSynthesisSource,
  ImageSynthesisPattern,
  ImageSynthesisContourPattern,
  ImageSynthesisContourMask,
  ImageSynthesisRet,
  ContoursRectData,
  ClearTrajectoryPathOpts,
  GetContoursRectDataOpts,
  CreateContourImageOpts,
} from '../interface/image.interface.js';
import { CACHE_KEY, JSON_TEMP_PATH } from '@/constants/index.js';
import { LockfileService } from '@/modules/lockfile/service/lockfile.service.js';
import { drawText } from './utils/draw-text.js';
import {
  getContoursRectCacheKey,
  getDecorativeImageCacheKey,
  getSourceImageWithOutContoursCacheKey,
} from './utils/cache.js';
import {
  // trajectoryForEach,
  getContoursRect,
  CONTOURS_TYPE,
  getFileType,
} from './utils/helper.js';
import {
  adjustCoordinatesForRotated,
  adjustPositionForRotation,
  degreesToRadians,
} from './utils/transform.js';
import { BaseService } from './base.service.js';
import { sharpService } from './sharp.service.js';
import { tryReadJson } from '@/core/utils/try-read-json.js';

@Provide()
export class ImageService {
  @Inject() private redisService: RedisService;

  @Inject() private cacheManager: CacheManager;

  @Inject() private sharpService: sharpService;

  @Inject() private baseService: BaseService;

  @Inject() private lockfileService: LockfileService;
  /** 款式图 数据缓存前缀 */
  styleImagePrefixKey: string;
  /**
   * 记录字体是是否已经注册过
   * 避免重复注册
   */
  registerFontSet: Set<string> = new Set();

  constructor() {
    this.styleImagePrefixKey = `${CACHE_KEY.STYLE_IMAGE}-`;
  }
  /**
   * 创建元素图，如文字、贴图等
   *
   * ps: 现在只有文字，图片暂时不考虑
   */
  async createElementImage({
    content,
    elementRect,
    styleData,
    font,
  }: CreateElementImageFnOpts) {
    const { x, y, width, height, rotate, scale, ratio, maxWidth } = elementRect;
    if (font) {
      const weight = (styleData['font-weight'] as string) || '';
      const style = (styleData['font-style'] as string) || '';
      const family = `${font.family}${weight}${style}`;
      // 没有注册过的字体才进行注册
      if (!this.registerFontSet.has(family)) {
        const dest = await this.baseService.downloadFont(font.path);
        registerFont(dest, {
          family: family,
          weight,
          style,
        });
        this.registerFontSet.add(family);
      }
    }

    const scaleFactor = (scale || 1) * (ratio || 1);
    let ctxWidth = Math.ceil(width * scaleFactor);
    let ctxHeight = Math.ceil(height * scaleFactor);
    // let ctxMaxWith = maxWith ? maxWith * scaleFactor : maxWith;
    let positionX = (x - (width * (scale || 1) - width) / 2) * (ratio || 1);
    let positionY = (y - (height * (scale || 1) - height) / 2) * (ratio || 1);

    const canvas = createCanvas(ctxWidth, ctxHeight);
    const context = canvas.getContext('2d');
    const clearRect = (w: number, h: number) => {
      canvas.width = w;
      canvas.height = h;
      context.clearRect(0, 0, w, h);
    };
    /**
     * 说明：
     * 因为canvas的 scale 方法只是改变图形的大小，而不是实际画布的大小
     *
     * 所以这里的放大、缩小、旋转的流程是
     * 1、计算实际文字绘制的大小
     * 2、将该大小 *scaleFactor ，等到实际放大、缩小后的画布大小
     * 3、如果有旋转的话，又将放大、缩小的画布大小重新计算
     * 4、绘制文字，并应用放大、缩小的值，将文字大小呈现为该画布的大小
     */
    const textData = drawText(
      context,
      {
        text: content,
        x: 0,
        y: 0,
        maxWidth,
      },
      styleData
    );
    const scaleTextWidth = textData.width * scaleFactor;
    const scaleTextHeight = textData.height * scaleFactor;

    if (rotate) {
      // 计算文字旋转后画布的大小
      const data = adjustCoordinatesForRotated(
        0,
        0,
        scaleTextWidth,
        scaleTextHeight,
        rotate
      );
      // 计算画布旋转后新尺寸相对原坐标的新坐标
      const positionData = adjustPositionForRotation(
        positionX,
        positionY,
        ctxWidth,
        ctxHeight,
        data.newWidth,
        data.newHeight
      );
      positionX = positionData.x;
      positionY = positionData.y;
      ctxWidth = data.newWidth;
      ctxHeight = data.newHeight;
      const radians = degreesToRadians(rotate);
      clearRect(ctxWidth, ctxHeight);
      context.translate(data.x, data.y);

      context.rotate(radians);
    } else {
      ctxWidth = Math.max(ctxWidth, scaleTextWidth);
      ctxHeight = Math.max(ctxHeight, scaleTextHeight);
      clearRect(ctxWidth, ctxHeight);
    }

    if (scaleFactor !== 1 && scaleFactor) {
      context.scale(scaleFactor, scaleFactor);
    }

    drawText(
      context,
      {
        text: content,
        x: 0,
        y: 0,
        maxWidth,
      },
      styleData
    );

    return {
      buffer: canvas.toBuffer('image/png'),
      positionX,
      positionY,
      width: ctxWidth,
      height: ctxHeight,
    };
  }
  /**
   * 获取轮廓
   * 优先获取缓存中数据
   */
  async getContoursRectWithCache(
    url: string,
    contoursType = CONTOURS_TYPE.BOTH
  ) {
    const { cacheKey, localCacheFailBackKey } = getContoursRectCacheKey(
      url,
      contoursType
    );

    const cacheManagerSet = (res: ContoursRectData) => {
      Promise.resolve().then(() => {
        this.cacheManager.set(cacheKey, res);
      });
    };

    let cacheData = await this.cacheManager.get<ContoursRectData>(cacheKey);
    if (cacheData) return cacheData;
    // 本地地址
    const tmpJsonFile = join(JSON_TEMP_PATH, `${cacheKey}.json`);
    // 尝试读取本地文件缓存
    cacheData = await tryReadJson<ContoursRectData>(tmpJsonFile);
    if (cacheData) {
      cacheManagerSet(cacheData);
      return cacheData;
    }
    // 尝试获取全部的数据
    if (localCacheFailBackKey) {
      const tmpJsonFailBackFile = join(
        JSON_TEMP_PATH,
        `${localCacheFailBackKey}.json`
      );
      cacheData = await tryReadJson<ContoursRectData>(tmpJsonFailBackFile);
      if (cacheData) {
        return cacheData;
      }
    }

    const imageBuffer = await this.baseService.downloadImage(url);
    const res = await getContoursRect(imageBuffer, contoursType);
    this.lockfileService.lockFile(
      () => fs.writeJSON(tmpJsonFile, res),
      cacheKey,
      200
    );
    cacheManagerSet(res);

    return res;
  }
  /**
   * 生成带背景的‘相框’图
   *
   * 注意 url 和 maskUrl 的大小应该一致
   */
  async createDecorativeImageWithMask(
    options: CreateDecorativeImageWithMaskOpts
  ): Promise<CreateDecorativeImageWithMaskCacheData> {
    const { maskUrl, color, url } = options;
    const { cacheKey } = getDecorativeImageCacheKey(url, maskUrl, color);
    // 尝试获取 redis 缓存
    const cacheData = await this.redisService.get(cacheKey);
    if (cacheData) {
      try {
        return JSON.parse(cacheData) as CreateDecorativeImageWithMaskCacheData;
      } catch (e) {}
    }

    const tmpJsonFile = join(JSON_TEMP_PATH, `${cacheKey}.json`);
    const localCache =
      await tryReadJson<CreateDecorativeImageWithMaskCacheData>(tmpJsonFile);
    if (localCache?.url) {
      return localCache;
    }
    const { imageWidth, imageHeight, trajectoryLine } =
      await this.getContoursRectWithCache(maskUrl, CONTOURS_TYPE.TRAJECTORY);
    const canvas = createCanvas(imageWidth, imageHeight);
    const context = canvas.getContext('2d');
    context.clearRect(0, 0, imageWidth, imageHeight);
    context.fillStyle = color;
    context.fillRect(0, 0, imageWidth, imageHeight);
    context.beginPath();
    trajectoryLine.forEach(lineList => {
      lineList.forEach(({ x, y, width }) => {
        context.clearRect(x, y, width, 1);
      });
    });
    // trajectoryForEach(trajectory, (startX, startY) => {
    //   context.clearRect(startX, startY, 1, 1);
    // });
    const image = await this.handleLoadImage(url);
    context.drawImage(image, 0, 0, imageWidth, imageHeight);
    const dataUrl = await this.baseService.uploadCanvasImage(canvas, 'png');
    const res = {
      url: dataUrl,
      width: imageWidth,
      height: imageHeight,
    };

    this.redisService.set(
      cacheKey,
      JSON.stringify(res),
      'EX',
      1 * 60 * 60 * 24 * 7
    );
    this.lockfileService.lockFile(
      () => fs.writeJSON(tmpJsonFile, res),
      cacheKey
    );

    return res;
  }
  /**
   * 获取轮廓数据
   */
  async getContoursRectData(opts: GetContoursRectDataOpts) {
    const { contourWidth, contourHeight, x, y, imageHeight, imageWidth } =
      await this.getContoursRectWithCache(
        opts.maskUrl,
        CONTOURS_TYPE.TRAJECTORY
      );

    return {
      width: imageWidth,
      height: imageHeight,
      contourWidth,
      contourHeight,
      x,
      y,
    };
  }
  private getZoom(data: ZoomValue = {}) {
    const { scale = 1, ratio = 1 } = data;
    return scale * ratio;
  }
  /**
   * 将元素图根据轮廓图数据，将元素图绘制在轮廓图上，并清除多余位置
   */
  private async getContourImageData(
    maskData: ImageSynthesisContourMask,
    patternData: ImageSynthesisContourPattern,
    getContourPattern = false
  ) {
    const {
      contourHeight,
      contourWidth,
      x,
      y,
      // whitePixelCount,
      // maxX,
      // maxY,
      // trajectory,
      // peripheralTrajectory,
      peripheralTrajectoryLine,
    } = await this.getContoursRectWithCache(
      maskData.url,
      CONTOURS_TYPE.PERIPHERAL_TRAJECTORY
    );
    let patternWidth = patternData.width;
    let patternHeight = patternData.height;

    const getSizeScale = (pw: number, ph: number, cw: number, ch: number) => {
      const sw = cw / pw;
      const sh = ch / ph;
      return Math.min(sw, sh);
    };
    /**
     * 如果没有传入指定尺寸
     *
     * 则获取元素图片尺寸，并等比缩放为轮廓可放下的尺寸
     */
    if (!patternWidth || !patternHeight) {
      const zoom = this.getZoom(patternData.zoom);
      const { width, height } = await this.baseService.getOssImageData(
        patternData.url
      );
      patternWidth = width * zoom;
      patternHeight = height * zoom;
      if (patternHeight !== contourHeight || patternWidth !== contourWidth) {
        const scale = getSizeScale(
          patternWidth,
          patternHeight,
          contourWidth,
          contourHeight
        );
        patternWidth *= Math.abs(scale);
        patternHeight *= Math.abs(scale);
      }
    }
    // 记录原始尺寸
    const originalPatternWidth = patternWidth;
    const originalPatternHeight = patternHeight;
    // 元素图片
    const patternDataUrl: string | Buffer = patternData.url;
    // 元素图片 buffer
    let imageBuffer: Buffer;
    // 有旋转角度
    if (patternData.rotate) {
      const rotateData = await this.sharpService.imageRotate(
        // 先将图片放大到指定大小
        await this.sharpService.handleGetImageBufferByWH(
          patternData.url,
          patternWidth, // 如果没有传大小，这个大小则为图片能完全放进轮廓的尺寸
          patternHeight
        ),
        patternData.rotate
      );
      // 更新大小等
      patternWidth = rotateData.width;
      patternHeight = rotateData.height;
      imageBuffer = rotateData.buffer;
      /**
       * 如果没有主动设置 width height
       * 也没有设置缩放比例
       * 则转换为 图片可以完全放进轮廓的尺寸
       */
      if (
        patternData.width == null &&
        patternData.height == null &&
        this.getZoom(patternData.zoom) === 1 &&
        (patternWidth > contourWidth || patternHeight > contourHeight)
      ) {
        const scale = getSizeScale(
          patternWidth,
          patternHeight,
          contourWidth,
          contourHeight
        );
        patternWidth *= scale;
        patternHeight *= scale;
      }
    } else {
      imageBuffer = await this.sharpService.handleGetImageBufferByWH(
        patternDataUrl,
        patternWidth,
        patternHeight
      );
    }

    // 偏移值
    let offsetX = patternData.x;
    let offsetY = patternData.y;
    /**
     * 这里如果没有指定偏移值，则计算居中
     * 如果是旋转了那也是包含
     *
     * 说明：offsetX offsetY都是基于画布的坐标，而并非是轮廓的
     *      如果需要基于轮廓的，需要额外处理，暂时不管
     */
    if (offsetX == null || offsetY == null) {
      offsetX = (contourWidth - patternWidth!) / 2;
      // 说明，这里是上下居中的计算，现在要置顶，所以直接 offsetY = 0
      // offsetY = (contourHeight - patternHeight!) / 2;
      offsetY = 0;
      offsetX += x;
      offsetY += y;
    } else if (patternData.rotate) {
      /**
       * 如果有有传，并且旋转了，则需要做转换
       */
      const positionData = adjustPositionForRotation(
        offsetX,
        offsetY,
        originalPatternWidth,
        originalPatternHeight,
        patternWidth,
        patternHeight
      );
      offsetX = positionData.x;
      offsetY = positionData.y;
    }
    const canvas = createCanvas(maskData.width, maskData.height);
    const context = canvas.getContext('2d');
    context.clearRect(0, 0, maskData.width, maskData.height);

    const image = await loadImage(imageBuffer);
    context.drawImage(image, offsetX, offsetY, patternWidth, patternHeight);
    context.beginPath();
    // let lastX = -1;
    // let lastY = -1;
    // let startX = -1;
    // let length = 0;
    // trajectoryForEach(peripheralTrajectory, (x, y) => {
    //   if (y === lastY && x === lastX + 1) {
    //     length++;
    //   } else {
    //     if (length > 0) {
    //       context.clearRect(startX, lastY, length, 1);
    //     }
    //     startX = x;
    //     length = 1;
    //   }
    //   lastX = x;
    //   lastY = y;
    // });
    peripheralTrajectoryLine.forEach(lineList => {
      lineList.forEach(({ x: x1, y: y1, width }) => {
        context.clearRect(x1, y1, width, 1);
      });
    });
    const buffer = canvas.toBuffer('image/png');
    let contourPattern = '';
    // 获取元素贴在轮廓中图片
    if (getContourPattern) {
      contourPattern = await this.sharpService.imageExtract({
        url: buffer,
        rect: {
          width: contourWidth!,
          height: contourHeight!,
          left: x,
          top: y,
        },
      });
    }

    return {
      /** 图片的宽 */
      imageWidth: patternWidth,
      /** 图片的高 */
      imageHeight: patternHeight,
      // 轨迹
      // trajectory,
      /** 轮廓宽度 */
      width: contourWidth,
      /** 轮廓高度 */
      height: contourHeight,
      /** 轮廓左上角的 x 坐标 */
      x,
      /** 轮廓左上角的 y 坐标 */
      y,
      /** 白色像素总数 */
      // whitePixelCount,
      /** 轮廓像素总数 */
      // maxX,
      // maxY,
      // patternImageData,
      offsetX,
      offsetY,
      buffer,
      contourPattern,
    };
  }
  async createContourImage(opts: CreateContourImageOpts) {
    const { maskData } = opts;

    if (maskData.height == null || maskData.width == null) {
      const { width, height } = await this.baseService.getOssImageData(
        maskData.url
      );
      maskData.width = width;
      maskData.height = height;
    }
    const res = await this.getContourImageData(
      maskData as any,
      opts.pattern,
      true
    );
    return res.contourPattern;
  }
  /**
   * 下载图片
   * 这里可能有些图片不符合node-canvas的格式(规则)，所以需要转换一下
   *
   * 如果是传Buffer数据，fileType 必传
   */
  private async handleLoadImage(url: string | Buffer, fileType?: string) {
    let sourceLocalBuffer: Buffer;
    if (typeof url === 'string') {
      sourceLocalBuffer = await this.baseService.downloadImage(url!);
    } else {
      sourceLocalBuffer = url;
    }
    let sourceImage: Image;
    try {
      sourceImage = await loadImage(sourceLocalBuffer);
    } catch (error) {
      // let fileType = url.split('?')[0].split('/').pop().split('.')[1];
      // fileType = fileType === 'jpg' || fileType === 'jpeg' ? 'jpeg' : 'png';
      if (!fileType && typeof url === 'string') {
        fileType = getFileType(url);
      }
      if (!fileType) {
        const metadata = await sharp(sourceLocalBuffer).metadata();
        fileType = metadata.format;
      }
      sourceImage = await loadImage(
        await sharp(sourceLocalBuffer)
          .toFormat(fileType as 'jpeg' | 'png')
          .toBuffer()
      );
    }
    return sourceImage;
  }
  /**
   * 将底图（现在的也是相框或者底衫图）和元素图合成
   */
  private async handleCreateImage(
    sourceData: Omit<ImageSynthesisSource, 'contextRect' | 'x' | 'y'>,
    patternData: ImageSynthesisPattern,
    /**
     * 绘制底图的时机，前置还是后置
     *
     * 合层 相框+元素 可使用后置，因为相框是透明图，可直接覆盖
     * 成图则是前置，非透明图 如果后置则会将元素信息覆盖
     */
    drawSourceOccasion: 'prefix' | 'suffix' = 'suffix',
    getContourPattern = false
  ) {
    // 此处 source, sourceMask 必定会存在，否则不会走到这里
    const { source, sourceMask } = sourceData;
    const { pattern } = patternData;
    const sourceZoom = this.getZoom(sourceData.zoom);
    const patternZoom = this.getZoom(patternData.zoom);
    // 画布（底图）大小
    let sourceWidth = sourceData.width;
    let sourceHeight = sourceData.height;
    if (!sourceWidth || !sourceHeight) {
      const sourceImageInfo = await this.baseService.getOssImageData(source!);
      sourceWidth = sourceImageInfo.width;
      sourceHeight = sourceImageInfo.height;
    } else {
      sourceWidth *= sourceZoom;
      sourceHeight *= sourceZoom;
    }
    if (!sourceMask) {
      throw new httpError.BadRequestError('sourceMask is required');
    }

    const data = await this.getContourImageData(
      {
        url: sourceMask!,
        width: sourceWidth,
        height: sourceHeight,
      },
      {
        url: pattern!,
        width: patternData.width && patternData.width * patternZoom,
        height: patternData.height && patternData.height * patternZoom,
        x: patternData.offsetX && patternData.offsetX * patternZoom,
        y: patternData.offsetY && patternData.offsetY * patternZoom,
        zoom: patternData.zoom,
        rotate: patternData.rotate,
      },
      getContourPattern
    );
    const canvas = createCanvas(sourceWidth, sourceHeight);
    const context = canvas.getContext('2d');
    // const sourceLocalBuffer = await this.downloadImage(source!);
    // const sourceImage = await this.handleLoadImage(source!);
    const sourceLocalBuffer = await this.sharpService.handleGetImageBufferByWH(
      source!,
      sourceWidth,
      sourceHeight
    );
    const sourceImage = await this.handleLoadImage(
      sourceLocalBuffer,
      getFileType(source)
    );

    if (drawSourceOccasion === 'prefix') {
      context.drawImage(sourceImage, 0, 0, sourceWidth, sourceHeight);
    }
    // 这里绘制元素图
    context.drawImage(
      await loadImage(data.buffer),
      0,
      0,
      sourceWidth,
      sourceHeight
    );
    if (drawSourceOccasion === 'suffix') {
      context.drawImage(sourceImage, 0, 0, sourceWidth, sourceHeight);
    }
    const buffer = canvas.toBuffer('image/png');

    return {
      contourPattern: data.contourPattern,
      buffer,
      width: sourceWidth,
      height: sourceHeight,
    };
  }
  /**
   * 图片合成
   */
  async imageSynthesis(opts: ImageSynthesisFnOpts) {
    const sourceData = opts.source;
    const patternData = opts.pattern;
    const otherData = opts.other || {};
    const { catchKey, elements, fileType = 'png' } = otherData;

    const imageCatchKey = catchKey?.trim()
      ? `${this.styleImagePrefixKey}${catchKey}`
      : '';
    const tmpJsonFile = join(JSON_TEMP_PATH, `${imageCatchKey}.json`);
    if (imageCatchKey) {
      const catchData = await this.redisService.get(imageCatchKey);
      if (catchData) {
        try {
          return JSON.parse(catchData) as ImageSynthesisRet;
        } catch (e) {}
      }
      const localCache = await tryReadJson<ImageSynthesisRet>(tmpJsonFile);
      if (localCache?.url) return localCache;
    }

    const { source, contextRect } = sourceData;
    if (!source && !contextRect) {
      throw new httpError.BadRequestError(
        '‘source’ 和 ‘contextRect’ 至少配置一个'
      );
    }
    // 底图缩放值
    const sourceZoom = this.getZoom(sourceData.zoom);
    const patternZoom = this.getZoom(patternData.zoom);
    // 默认 元素图
    let patternImageUrl: Image | string = patternData.pattern;
    let imageWidth: number;
    let imageHeight: number;
    let contourPattern = '';
    // 旋转后的x位置
    let offsetXWithRotate: number;
    // 旋转后的y位置
    let offsetYWithRotate: number;

    const getX = () => {
      if (source) {
        return sourceData.x ? sourceData.x * sourceZoom : 0;
      }
      return patternData.offsetX ? patternData.offsetX * patternZoom : 0;
    };
    const getY = () => {
      if (source) {
        return sourceData.y ? sourceData.y * sourceZoom : 0;
      }
      return patternData.offsetY ? patternData.offsetY * patternZoom : 0;
    };

    // 有底图，走图片合成逻辑
    if (source) {
      // 这里已经做了 zoom 的转换
      const {
        buffer,
        width,
        height,
        contourPattern: _contourPattern,
      } = await this.handleCreateImage(
        sourceData,
        patternData,
        otherData?.drawSourceOccasion,
        otherData?.getContourPattern
      );
      patternImageUrl = await loadImage(buffer);
      imageWidth = width;
      imageHeight = height;
      contourPattern = _contourPattern;
    } else {
      const { width, height } = await this.baseService.getOssImageData(
        patternImageUrl
      );
      imageWidth = patternData.width ? patternData.width * patternZoom : width;
      imageHeight = patternData.height
        ? patternData.height * patternZoom
        : height;
      if (patternData.rotate) {
        const rotateData = await this.sharpService.imageRotate(
          // 先将图片放大到指定大小
          await this.sharpService.handleGetImageBufferByWH(
            patternImageUrl,
            imageWidth,
            imageHeight
          ),
          patternData.rotate
        );

        const positionData = adjustPositionForRotation(
          getX(),
          getY(),
          imageWidth,
          imageHeight,
          rotateData.width,
          rotateData.height
        );
        imageWidth = rotateData.width;
        imageHeight = rotateData.height;
        offsetXWithRotate = positionData.x;
        offsetYWithRotate = positionData.y;
        patternImageUrl = await loadImage(rotateData.buffer);
      }
    }

    if (!patternImageUrl) {
      throw new httpError.BadRequestError('元素图为空');
    }
    let contextRectWidth = contextRect?.width;
    let contextRectHeight = contextRect?.height;
    if (contextRectWidth && contextRectHeight) {
      contextRectWidth *= sourceZoom;
      contextRectHeight *= sourceZoom;
    } else {
      contextRectWidth = imageWidth;
      contextRectHeight = imageHeight;
    }
    const x = offsetXWithRotate != null ? offsetXWithRotate : getX();
    const y = offsetYWithRotate != null ? offsetYWithRotate : getY();
    const canvas = createCanvas(contextRectWidth, contextRectHeight);
    const context = canvas.getContext('2d');
    if (typeof patternImageUrl === 'string') {
      context.drawImage(
        // await loadImage(await this.downloadImage(patternImageUrl)),
        await this.handleLoadImage(patternImageUrl),
        x,
        y,
        imageWidth,
        imageHeight
      );
    } else {
      context.drawImage(patternImageUrl, x, y, imageWidth, imageHeight);
    }

    if (elements?.length) {
      for (const element of elements) {
        const res = await this.createElementImage(element);
        context.drawImage(
          await loadImage(res.buffer),
          res.positionX,
          res.positionY,
          res.width,
          res.height
        );
      }
    }
    const url = await this.baseService.uploadCanvasImage(canvas, fileType);
    const ret: ImageSynthesisRet = {
      contourPattern,
      url,
      width: canvas.width,
      height: canvas.height,
    };

    if (imageCatchKey) {
      this.redisService.set(
        imageCatchKey,
        JSON.stringify(ret),
        'EX',
        1 * 60 * 60 * 24 * 7
      );
      this.lockfileService.lockFile(
        () => fs.writeJson(tmpJsonFile, ret),
        tmpJsonFile
      );
    }
    return ret;
  }
  /**
   * 根据蒙层将图片对应的轨迹数据去除
   */
  async clearTrajectoryPath(opts: ClearTrajectoryPathOpts) {
    const { url, maskUrl } = opts;
    const { cacheKey } = getSourceImageWithOutContoursCacheKey(url, maskUrl);
    const cacheData = await this.redisService.get(cacheKey);
    if (cacheData) return cacheData;

    const {
      imageWidth,
      imageHeight,
      trajectoryLine,
      contourWidth,
      contourHeight,
      x,
      y,
    } = await this.getContoursRectWithCache(maskUrl, CONTOURS_TYPE.TRAJECTORY);
    const canvas = createCanvas(imageWidth, imageHeight);
    const context = canvas.getContext('2d');
    context.clearRect(0, 0, imageWidth, imageHeight);
    const image = await this.handleLoadImage(url);
    context.drawImage(image, 0, 0, imageWidth, imageHeight);

    context.beginPath();
    trajectoryLine.forEach(lineList => {
      lineList.forEach(({ x, y, width }) => {
        context.clearRect(x, y, width, 1);
      });
    });
    const res = await this.baseService.uploadCanvasImage(canvas, 'png');
    this.redisService.set(cacheKey, res, 'EX', 1 * 60 * 60 * 24 * 7);

    return {
      url: res,
      width: imageWidth,
      height: imageHeight,
      contourWidth,
      contourHeight,
      x,
      y,
    };
  }
}
