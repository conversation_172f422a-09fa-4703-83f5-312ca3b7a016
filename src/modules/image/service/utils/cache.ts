import { generateShortKey } from '@/core/utils/index.js';
import { CONTOURS_TYPE } from './helper.js';
import { CACHE_KEY } from '@/constants/index.js';

/**
 * 获取轨迹相关缓存 key
 */
export const getContoursRectCacheKey = (
  url: string,
  contoursType = CONTOURS_TYPE.BOTH
) => {
  /**
   * 版本，如果后面的算法发现变化，可以增加版本号，这样就可以更新缓存
   */
  const version = '1.0.0';
  const urlShort = generateShortKey(url);
  const contoursRectPrefixKey = `${CACHE_KEY.CONTOURS_RECT}-`;
  /**
   * 当前缓存key
   */
  const cacheKey = `${contoursRectPrefixKey}${urlShort}-${contoursType}-${version}`;
  /**
   * failBack 缓存key
   *
   * 本地地址兜底文件地址
   *
   * 如果 `contoursType == CONTOURS_TYPE.BOTH` 的话，那么这个 key 为空
   *
   * 其实就是那全部信息的那块，在定时任务中，是下载全部的
   */
  const localCacheFailBackKey =
    contoursType !== CONTOURS_TYPE.BOTH
      ? `${contoursRectPrefixKey}${urlShort}-${CONTOURS_TYPE.BOTH}-${version}`
      : '';

  return {
    cacheKey,
    localCacheFailBackKey,
  };
};

/**
 * 获取带背景相框图对应缓存 key
 */
export const getDecorativeImageCacheKey = (
  url: string,
  maskUrl: string,
  color: string
) => {
  const version = '1.1.1';
  const imageWithBgPrefixKey = `${CACHE_KEY.IMAGE_WITH_BG}-`;
  const urlShortKey = generateShortKey(url);
  const maskUrlShortKey = generateShortKey(maskUrl);
  const cacheKey = `${imageWithBgPrefixKey}${urlShortKey}-${maskUrlShortKey}-${color}-${version}`;

  return {
    cacheKey,
  };
};

export const getSourceImageWithOutContoursCacheKey = (
  url: string,
  maskUrl: string
) => {
  const sourceImageWithOutContoursPrefixKey = `${CACHE_KEY.SOURCE_IMAGE_WITH_OUT_CONTOURS}-`;
  const urlShortKey = generateShortKey(url);
  const maskUrlShortKey = generateShortKey(maskUrl);
  const cacheKey = `${sourceImageWithOutContoursPrefixKey}${urlShortKey}-${maskUrlShortKey}`;

  return {
    cacheKey,
  };
};

export const getUrlSizeResizeCacheKey = (
  maskUrl: string,
  width: number,
  height: number
) => {
  const cachePrefixKey = `${CACHE_KEY.MASK_URL_SIZE_RESIZE}-`;
  const maskUrlShortKey = generateShortKey(maskUrl);
  const cacheKey = `${cachePrefixKey}-${maskUrlShortKey}&${width}_${height}`;

  return {
    cacheKey,
  };
};
