import sharp from 'sharp';
import { httpError } from '@midwayjs/core';
import {
  Point,
  ContoursRectData,
  TrajectoryLine,
} from '../../interface/image.interface.js';

export const trajectoryForEach = (
  trajectory: Point[],
  handler: (x: number, y: number, index: number) => void
) => {
  for (let index = 0; index < trajectory.length; index++) {
    const item = trajectory[index];
    handler(item.x, item.y, index);
  }
};

/**
 * 获取图形轮廓信息
 */
// export const getContoursRect = async (imageBuffer: Buffer) => {
//   const image = sharp(imageBuffer);
//   const imageMetadata = await image.metadata();
//   if (!imageMetadata.width || !imageMetadata.height) {
//     throw new httpError.BadRequestError('Unable to retrieve image metadata.');
//   }
//   const raw = await image
//     .grayscale() // 转换为灰度
//     .raw() // 获取原始像素数据
//     .toBuffer();
//   // 初始化变量
//   // let whitePixelCount = 0;
//   let minX = imageMetadata.width;
//   let minY = imageMetadata.height;
//   let maxX = 0;
//   let maxY = 0;
//   const trajectory: Point[] = [];
//   const peripheralTrajectory: Point[] = [];
//   const threshold = 128;

//   for (let i = 0; i < raw.length; i++) {
//     const gray = raw[i]; // 灰度值
//     const color = gray > threshold ? 255 : 0;
//     const x = i % imageMetadata.width;
//     const y = Math.floor(i / imageMetadata.width);

//     if (color === 255) {
//       // whitePixelCount++;
//       if (x < minX) minX = x;
//       if (y < minY) minY = y;
//       if (x > maxX) maxX = x;
//       if (y > maxY) maxY = y;
//       trajectory.push({ x, y });
//     } else {
//       peripheralTrajectory.push({ x, y });
//     }
//   }

//   const contourWidth = maxX - minX;
//   const contourHeight = maxY - minY;

//   const data: ContoursRectData = {
//     trajectory,
//     peripheralTrajectory,
//     contourWidth,
//     contourHeight,
//     x: minX,
//     y: minY,
//     imageWidth: imageMetadata.width,
//     imageHeight: imageMetadata.height,
//   };
//   return data;
// };

/**
 * 获取轮廓类型
 */
export enum CONTOURS_TYPE {
  /** 获取全部轮廓 */
  BOTH = 1,
  /** 获取内轮廓 */
  TRAJECTORY = 2,
  /** 获取外轮廓 */
  PERIPHERAL_TRAJECTORY = 3,
}
/**
 * 获取图形轮廓信息
 * @param imageBuffer 图片buffer
 * @param contoursType 获取轮廓类型 @default CONTOURS_TYPE.BOTH
 */
export const getContoursRect = async (
  imageBuffer: Buffer,
  contoursType = CONTOURS_TYPE.BOTH
) => {
  const image = sharp(imageBuffer);
  const imageMetadata = await image.metadata();
  if (!imageMetadata.width || !imageMetadata.height) {
    throw new httpError.BadRequestError('Unable to retrieve image metadata.');
  }
  const raw = await image
    .grayscale() // 转换为灰度
    .raw() // 获取原始像素数据
    .toBuffer();

  let minX = imageMetadata.width;
  let minY = imageMetadata.height;
  let maxX = 0;
  let maxY = 0;
  const trajectoryLine: TrajectoryLine = [];
  const peripheralTrajectoryLine: TrajectoryLine = [];
  const threshold = 128;

  const lineMap = new Map<number, { x: number; width: number }[]>();
  const peripheralLineMap = new Map<number, { x: number; width: number }[]>();
  const isGetTrajectory =
    contoursType === CONTOURS_TYPE.BOTH ||
    contoursType === CONTOURS_TYPE.TRAJECTORY;
  const isGetPeripheralTrajectory =
    contoursType === CONTOURS_TYPE.BOTH ||
    contoursType === CONTOURS_TYPE.PERIPHERAL_TRAJECTORY;

  for (let i = 0; i < raw.length; i++) {
    const gray = raw[i]; // 灰度值
    const color = gray > threshold ? 255 : 0;
    const x = i % imageMetadata.width;
    const y = Math.floor(i / imageMetadata.width);

    if (color === 255) {
      if (x < minX) minX = x;
      if (y < minY) minY = y;
      if (x > maxX) maxX = x;
      if (y > maxY) maxY = y;

      if (isGetTrajectory) {
        if (!lineMap.has(y)) {
          lineMap.set(y, []);
        }
        const line = lineMap.get(y)!;
        if (
          line.length === 0 ||
          line[line.length - 1].x + line[line.length - 1].width !== x
        ) {
          line.push({ x, width: 1 });
        } else {
          line[line.length - 1].width++;
        }
      }
    } else if (isGetPeripheralTrajectory) {
      // 处理非白色像素，生成 peripheralLineMap
      if (!peripheralLineMap.has(y)) {
        peripheralLineMap.set(y, []);
      }
      const peripheralLine = peripheralLineMap.get(y)!;
      if (
        peripheralLine.length === 0 ||
        peripheralLine[peripheralLine.length - 1].x +
          peripheralLine[peripheralLine.length - 1].width !==
          x
      ) {
        peripheralLine.push({ x, width: 1 });
      } else {
        peripheralLine[peripheralLine.length - 1].width++;
      }
    }
  }

  // 转换 lineMap 和 peripheralLineMap 为轨迹数组
  if (isGetTrajectory) {
    lineMap.forEach((lines, y) => {
      trajectoryLine.push(lines.map(line => ({ ...line, y })));
    });
  }

  if (isGetPeripheralTrajectory) {
    peripheralLineMap.forEach((lines, y) => {
      peripheralTrajectoryLine.push(lines.map(line => ({ ...line, y })));
    });
  }

  const contourWidth = maxX - minX;
  const contourHeight = maxY - minY;

  const data: ContoursRectData = {
    trajectoryLine,
    peripheralTrajectoryLine,
    contourWidth,
    contourHeight,
    x: minX,
    y: minY,
    imageWidth: imageMetadata.width,
    imageHeight: imageMetadata.height,
  };
  return data;
};

export const getFileType = (url: string) => {
  let fileType = url.split('?')[0].split('/').pop().split('.')[1];
  fileType = fileType === 'jpg' || fileType === 'jpeg' ? 'jpeg' : 'png';
  return fileType;
};
