export const hexToRgba = (hex: string) => {
  // 移除HEX颜色值的开头的#符号（如果存在）
  hex = hex.replace(/^#/, '');
  // 如果是三位简写形式的HEX颜色值，将其转换为六位形式
  if (hex.length === 3) {
    hex = hex.split('').map(char => char + char).join('');
  }
  // 是否是 8 位含有透明度通道
  const hasAlpha = hex.length === 8;
  let r: number, g: number, b: number, a: number;

  if (!hasAlpha) {
    // 解析HEX颜色值中的红色（R）、绿色（G）、蓝色（B）分量
    r = parseInt(hex.substring(0, 2), 16);
    g = parseInt(hex.substring(2, 4), 16);
    b = parseInt(hex.substring(4, 6), 16);
    a = 255;
  } else {
    r = parseInt(hex.slice(0, 2), 16);
    g = parseInt(hex.slice(2, 4), 16);
    b = parseInt(hex.slice(4, 6), 16);
    a = parseInt(hex.slice(6, 8), 16);
  }

  return { r, g, b, a };
};

export const rgbToHex = (r: number, g: number, b: number) => {
  // 将单个颜色分量转换为两位的HEX字符串
  const toHex = (color: number) => {
    // 使用toString(16)将数值转换为十六进制字符串
    // padStart(2, '0')确保结果为两位字符串，不足两位时前面补0
    return color.toString(16).padStart(2, '0');
  };

  // 使用前面定义的toHex函数转换每个颜色分量，然后将它们拼接成完整的HEX颜色字符串
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
};

/**
 * 角度转弧度
 */
export const degreesToRadians = (degrees: number) => {
  return degrees * (Math.PI / 180);
};

/**
 * 计算旋转后的 大小
 */
export const adjustCoordinatesForRotated = (
  x: number,
  y: number,
  originalWidth: number,
  originalHeight: number,
  angle: number,
  textWidth?: number,
  textHeight?: number,
) => {
  // 将角度转换为弧度
  const radians = degreesToRadians(angle);
  let rotatedTextWidth = 0;
  let rotatedTextHeight = 0;

  // 计算文字旋转后的宽高
  if (textWidth && textHeight) {
    rotatedTextWidth
    = Math.abs(textWidth * Math.cos(radians)) + Math.abs(textHeight * Math.sin(radians));
    rotatedTextHeight
      = Math.abs(textHeight * Math.cos(radians)) + Math.abs(textWidth * Math.sin(radians));
  }

  // 计算对角线，获取旋转后的新画布尺寸
  const newWidth = Math.max(
    Math.abs(
      originalWidth * Math.cos(radians)) + Math.abs(originalHeight * Math.sin(radians),
    ),
    rotatedTextWidth,
  );
  const newHeight = Math.max(
    Math.abs(
      originalHeight * Math.cos(radians)) + Math.abs(originalWidth * Math.sin(radians),
    ),
    rotatedTextHeight,
  );

  // 计算新旧画布中心偏移量
  const deltaX = (newWidth - originalWidth) / 2;
  const deltaY = (newHeight - originalHeight) / 2;

  // 在旋转后坐标系下调整绘制坐标
  // 原理是将坐标平移到新中心，然后进行旋转
  const centerX = originalWidth / 2;
  const centerY = originalHeight / 2;

  // 将绘制坐标从左上角平移到中心点
  const translatedX = x - centerX;
  const translatedY = y - centerY;

  // 进行旋转后的坐标计算
  const rotatedX = translatedX * Math.cos(radians) - translatedY * Math.sin(radians);
  const rotatedY = translatedX * Math.sin(radians) + translatedY * Math.cos(radians);

  // 将坐标平移回新的画布中心
  const adjustedX = rotatedX + newWidth / 2;
  const adjustedY = rotatedY + newHeight / 2;

  return {
    x: adjustedX,
    y: adjustedY,
    deltaX,
    deltaY,
    newWidth,
    newHeight,
    centerX,
    centerY,
    rotatedX,
    rotatedY,
    rotatedTextWidth,
    rotatedTextHeight,
  };
};

/**
 * 获取旋转后新画布的新坐标
 * 相对中心位置不变
 *
 * @param originalX 原始页面定位的 x
 * @param originalY 原始页面定位的 y
 * @param originalWidth 旋转前的宽度
 * @param originalHeight 旋转前的高度
 * @param newWidth 旋转后的宽度
 * @param newHeight 旋转后的高度
 */
export const adjustPositionForRotation = (
  originalX: number,
  originalY: number,
  originalWidth: number,
  originalHeight: number,
  newWidth: number,
  newHeight: number,
) => {
  // 计算旋转前的中心点
  const centerX = originalX + originalWidth / 2;
  const centerY = originalY + originalHeight / 2;

  // 计算旋转后的新页面位置，使得中心点不变
  const newX = centerX - newWidth / 2;
  const newY = centerY - newHeight / 2;

  return { x: newX, y: newY };
};

/**
 * 获取旋转后新的左上角坐标
 */
export const getRotatedCoordinates = (
  /** 左上角 x */
  x: number,
  /** 左上角 y */
  y: number,
  /** 中心 x */
  cx: number,
  /** 中心 y */
  cy: number,
  /** 旋转角度 */
  angle: number,
) => {
  const radians = degreesToRadians(angle);

  // 平移到旋转中心
  const translatedX = x - cx;
  const translatedY = y - cy;

  const rotatedX = translatedX * Math.cos(radians) - translatedY * Math.sin(radians);
  const rotatedY = translatedX * Math.sin(radians) + translatedY * Math.cos(radians);

  // 平移回原坐标系
  const newX = rotatedX + cx;
  const newY = rotatedY + cy;

  return {
    x: newX,
    y: newY,
  };
};
