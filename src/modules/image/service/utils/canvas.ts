import { Canvas } from 'canvas';
import fs from 'fs-extra';
import FormData from 'form-data';
import { join } from 'node:path';
import { Readable } from 'node:stream';
// import crypto from 'crypto';
import { IMAGES_TEMP_PATH } from '@/constants/index.js';
import { ImageFileType } from '../../interface/image.interface.js';

export const ImageType = {
  JPEG: 'image/jpeg',
  PNG: 'image/png',
} as const;

export const getFileName = () => {
  // return `${crypto.randomBytes(4).toString('hex')}`;
  return `${Date.now()}-${Math.floor(Math.random() * 10000)}`;
};

/**
 * 将画布保存为图片文件
 *
 * @param canvas 画布对象
 * @param fileType 图片类型（'png' 或 'jpeg'）
 */
export const saveCanvasImage = (canvas: Canvas, fileType: ImageFileType) => {
  const filePath = join(IMAGES_TEMP_PATH, `/${getFileName()}.${fileType}`);
  return new Promise<{ filePath: string; unlinkTempFile: () => void }>(
    (resolve, reject) => {
      const writeStream = fs.createWriteStream(filePath);
      const bufferStream =
        fileType === 'png'
          ? canvas.createPNGStream()
          : canvas.createJPEGStream();

      bufferStream
        .pipe(writeStream)
        .on('finish', () => {
          const unlinkTempFile = () => {
            try {
              fs.unlink(filePath);
            } catch (error) {}
          };
          resolve({
            filePath,
            unlinkTempFile,
          });
        })
        .on('error', err => {
          reject(err);
        });
    }
  );
};

export const getCanvasFileStream = (
  canvas: Canvas,
  fileType: ImageFileType
) => {
  const bufferStream =
    fileType === 'png'
      ? canvas.createPNGStream({
          filters: canvas.PNG_ALL_FILTERS,
          resolution: 300,
        })
      : canvas.createJPEGStream({
          quality: 1,
          chromaSubsampling: false,
        });
  return bufferStream;
};

/**
 * 获取 canvas buffer
 */
export const getCanvasBuffer = (canvas: Canvas, fileType: ImageFileType) => {
  const bufferStream = getCanvasFileStream(canvas, fileType);
  return new Promise<Buffer>((resolve, reject) => {
    const buffers: Buffer[] = [];
    bufferStream.on('data', chunk => {
      buffers.push(chunk);
    });
    bufferStream.on('end', () => {
      resolve(Buffer.concat(buffers));
    });
    bufferStream.on('error', reject);
  });
};

/**
 * 将Canvas对象转换为文件流，并生成FormData对象。
 */
export const getCanvasFormData = async (
  canvas: Canvas,
  fileType: ImageFileType
) => {
  // const { filePath, unlinkTempFile } = await saveCanvasImage(canvas, fileType);
  // const fileStream = fs.createReadStream(filePath);
  const bufferStream = getCanvasFileStream(canvas, fileType);

  const formData = new FormData();
  formData.append('files', bufferStream, {
    filename: `${getFileName()}.${fileType}`,
  });

  return {
    formData,
    // unlinkTempFile,
  };
};

/**
 * 将 ArrayBuffer 写入到指定文件路径的文件中
 *
 * @param buffer ArrayBuffer 对象，需要写入文件的数据
 * @param filePath 字符串，文件路径
 */
export const streamArrayBufferToFile = (
  buffer: ArrayBuffer,
  filePath: string
) => {
  const readableStream = new Readable();
  readableStream.push(buffer);
  // 表示流结束
  readableStream.push(null);
  const writableStream = fs.createWriteStream(filePath);

  return new Promise<void>((resolve, reject) => {
    readableStream
      .pipe(writableStream)
      .on('finish', resolve)
      .on('error', reject);
  });
};
