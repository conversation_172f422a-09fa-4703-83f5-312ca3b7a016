import { CanvasRenderingContext2D } from 'canvas';
import { toUtf8CStyle } from '@/core/utils/index.js';

interface TextData {
  text: string;
  x: number;
  y: number;
  maxWidth?: number;
}

// 辅助函数：测量带字间距的文本宽度
const measureTextWithSpacing = (
  ctx: CanvasRenderingContext2D,
  text: string,
  letterSpacing: number
): number => {
  const textWidth = ctx.measureText(text).width;
  // 字间距总长度
  const additionalSpacing = (text.length - 1) * letterSpacing;
  return textWidth + additionalSpacing;
};

/**
 * 文字处理换行问题
 */
const wrapText = (
  ctx: CanvasRenderingContext2D,
  textData: TextData,
  letterSpacing: number
): string[] => {
  const { maxWidth, text } = textData;
  if (!maxWidth) {
    return text.split('\n');
  }

  const words = text.split('');
  let line = '';
  const lines: string[] = [];

  for (let i = 0; i < words.length; i++) {
    if (words[i] === '\n') {
      lines.push(line);
      line = '';
      continue;
    }

    const testLine = line + words[i];
    const testWidth = measureTextWithSpacing(ctx, testLine, letterSpacing);

    if (testWidth > maxWidth && i > 0) {
      lines.push(line); // 当前行已满，推入结果
      line = words[i]; // 开始新的一行
    } else {
      line = testLine;
    }
  }

  lines.push(line); // 推入最后一行
  return lines;
};

const getPxVal = (val: string | number) => {
  return typeof val === 'string' ? parseFloat(val.replace('px', '')) : val;
};

const getFontSizeVal = (v: string | number) => {
  return v || '12px';
};

const getLineHeight = (styleData: Record<string, string | number>) => {
  const val = styleData['line-height'];
  if (val) {
    return getPxVal(val);
  }
  return getPxVal(getFontSizeVal(styleData['font-size'])) * 1.2;
};

/**
 * 处理字间距问题
 *
 * 这里已经是写文字部分了
 */
// const letterSpacing = (
//   ctx: CanvasRenderingContext2D,
//   letterSpacingVal: number | string,
//   textData: TextData,
// ) => {
//   const { text, x, y } = textData;

//   // 暂时只支持 px 单位
//   const lsVal = getPxVal(letterSpacingVal);
//   for (let i = 0; i < text.length; i++) {
//     // 计算每个字符的x坐标，添加letterSpacing
//     const xPos = x + (i * (ctx.measureText(text[i]).width + lsVal));
//     ctx.fillText(text[i], xPos, y);
//   }
// };

const fontStyle = (
  context: CanvasRenderingContext2D,
  styleData: Record<string, string | number>
) => {
  /**
   * font-style、font-variant 和 font-weight 必须在 font-size 之前
      font-variant 只可以使用 CSS 2.1 定义的值，即 normal 和 small-caps
      font-stretch 必须是单个关键字值
      line-height 必须跟在 font-size 后面，由“/”分隔，例如“16px/3”
      font-family 必须最后指定
   */
  const vals: (string | number)[] = [];
  const fontAttrs = [
    // 'font-stretch',
    'font-style',
    'font-variant',
    'font-weight',
    'font-size',
    // 'line-height',
    'font-family',
  ];
  const defaultValue = {
    'font-style': 'normal',
    'font-variant': 'normal',
    'font-weight': 'normal',
  };
  fontAttrs.forEach(cssAttr => {
    let val = styleData[cssAttr] || defaultValue[cssAttr];
    if (cssAttr === 'font-size') {
      // font-size 对应 web 的默认值
      val = getFontSizeVal(val);
      // 补上单位
      val = typeof val === 'number' ? `${val}px` : val;
    }
    if (cssAttr === 'font-family') {
      if (!val) {
        // 默认字体
        val = 'Arial';
      } else {
        const weight = (styleData['font-weight'] as string) || '';
        const style = (styleData['font-style'] as string) || '';
        const family = `${val}${weight}${style}`;
        // 转义
        val = toUtf8CStyle(family);
      }
    }
    if (cssAttr === 'line-height') {
      const preIndex = vals.length - 1;
      // 获取 font-size
      const preVal = vals[preIndex];
      val = `${preVal}/${val || `${getLineHeight(styleData)}px`}` as string;
      vals.splice(preIndex, 1, val);
    } else {
      vals.push(val);
    }
  });
  context.font = vals
    .map(i => (i === 'initial' ? '' : i))
    .filter(Boolean)
    .join(' ');
};

// 解析 text-shadow 属性
const parseTextShadow = (textShadow: string) => {
  const shadowPattern =
    /([0-9]+px)\s([0-9]+px)\s([0-9]+px)\s(rgb\(\d+,\d+,\d+\)|#[0-9a-fA-F]{3,6}|[a-zA-Z]+)/;
  const match = textShadow.match(shadowPattern);

  if (match) {
    const offsetX = parseInt(match[1], 10); // 水平偏移
    const offsetY = parseInt(match[2], 10); // 垂直偏移
    const blurRadius = parseInt(match[3], 10); // 模糊半径
    const color = match[4]; // 颜色

    return { offsetX, offsetY, blurRadius, color };
  }
};

enum TEXT_DECORATION_VALUE {
  NONE = 'none',
  UNDERLINE = 'underline',
  LINE_THROUGH = 'line-through',
  OVERLINE = 'overline',
}
/**
 * 绘制 text-decoration
 */
const textDecoration = (
  context: CanvasRenderingContext2D,
  textData: Omit<TextData, 'maxWidth' | 'text'>,
  textDecorationVal: string,
  fontSize: number,
  lineWidth: number
) => {
  if (textDecorationVal === TEXT_DECORATION_VALUE.NONE) {
    return;
  }
  const { x, y } = textData;
  context.strokeStyle = context.fillStyle;
  context.lineWidth = Math.max(1, fontSize / 10);

  // 基于 alphabetic，顶线位置
  switch (textDecorationVal) {
    case TEXT_DECORATION_VALUE.UNDERLINE:
      // 下划线在文本底部绘制
      context.moveTo(x, y + context.lineWidth + context.lineWidth / 2);
      context.lineTo(
        x + lineWidth,
        y + context.lineWidth + context.lineWidth / 2
      );
      break;
    case TEXT_DECORATION_VALUE.LINE_THROUGH:
      // 中划线穿过文本中间
      // context.moveTo(x, y - fontSize / 3);
      // context.lineTo(x + lineWidth, y - fontSize / 3);
      // context.moveTo(x, y + 2 + context.lineWidth);
      // context.lineTo(x + lineWidth, y + 2 + context.lineWidth);
      console.warn('中划线暂没实现');
      break;
    case TEXT_DECORATION_VALUE.OVERLINE:
      // 上划线在文本顶部绘制
      // context.moveTo(x, y - fontSize);
      // context.lineTo(x + lineWidth, y - fontSize);
      // context.moveTo(x, mh);
      // context.lineTo(x + lineWidth, mh);
      console.warn('上划线暂没实现');
      break;
  }
  context.stroke();
};

/**
 * 绘制文字
 */
export const drawText = (
  context: CanvasRenderingContext2D,
  _textData: TextData,
  styleData: Record<string, string | number>
) => {
  const textData = { ..._textData };
  const fontSize = getPxVal(getFontSizeVal(styleData['font-size']));
  context.textBaseline = 'alphabetic';
  /**
   * 将 css 转换为 canvas 属性
   */
  const style2CanvasAttrs = {
    fillStyle: 'color',
    textAlign: 'text-align',
    globalAlpha: 'opacity',
  };
  Object.entries(style2CanvasAttrs).forEach(([attr, cssAttr]) => {
    let val = styleData[cssAttr];
    if (val != null) {
      if (
        cssAttr === style2CanvasAttrs.globalAlpha &&
        typeof val === 'string' &&
        val.endsWith('%')
      ) {
        const numberPart = parseFloat(val.slice(0, -1)) / 100;
        val = Number.isNaN(numberPart) ? 1 : numberPart;
      }
      context[attr] = val;
    }
  });
  fontStyle(context, styleData);
  const textShadowSetVal = styleData['text-shadow'];

  if (textShadowSetVal && textShadowSetVal !== 'none') {
    const shadowProps = parseTextShadow(textShadowSetVal as string);
    if (shadowProps) {
      const { offsetX, offsetY, blurRadius, color } = shadowProps;
      context.shadowOffsetX = offsetX;
      context.shadowOffsetY = offsetY;
      context.shadowBlur = blurRadius;
      context.shadowColor = color;
    }
  }

  const letterSpacingVal = getPxVal(styleData['letter-spacing'] || 1) as number;
  const lineHeight = getLineHeight(styleData);
  const textLines = wrapText(context, textData, letterSpacingVal);
  const textDecorationVal = styleData['text-decoration'];
  // 记录总宽度和高度
  let totalHeight = 0;
  let maxWidth = 0;
  // 绘制每行文本
  textLines.forEach((line, index) => {
    // 绘制每一行的字符，考虑字间距
    let currentX = textData.x;
    // 这一行的高度（最高值）
    const metrics = context.measureText(line);
    /**
     * 在小程序中，actualBoundingBoxAscent可能获取不到
     * 只能使用 actualBoundingBoxAscent 计算近似值
     * 通常 actualBoundingBoxAscent 小于或等于 fontBoundingBoxAscent
     */
    const lineActualBoundingBoxAscent =
      metrics.actualBoundingBoxAscent ?? metrics.fontBoundingBoxAscent * 0.9;
    /**
     * 文字标准 Y 轴位置
     *
     * 只针对 textBaseline = 'alphabetic'
     */
    const standardY =
      textData.y +
      lineActualBoundingBoxAscent +
      index * lineHeight +
      (lineHeight - lineActualBoundingBoxAscent) / 2;
    let _lineWidth = 0;
    for (const char of line) {
      const { width } = context.measureText(char);
      context.fillText(char, currentX, standardY);
      // 字符宽度 + 字间距
      currentX += width + letterSpacingVal;
      _lineWidth += width + letterSpacingVal;
    }
    // 更新最大宽度
    maxWidth = Math.max(maxWidth, _lineWidth);
    if (textDecorationVal) {
      // 计算每行文字的宽度
      const lineWidth = measureTextWithSpacing(context, line, letterSpacingVal);
      textDecoration(
        context,
        {
          // TODO: 如果还有文字居中的情况，还得处理
          x: textData.x,
          y: standardY,
        },
        textDecorationVal as string,
        fontSize,
        lineWidth
      );
    }
    totalHeight = (index + 1) * lineHeight;
  });

  return {
    width: maxWidth,
    // 这里如有使用下划线，则补充下划线的大小，totalHeight 是所有文字的高度
    height: totalHeight + (textDecorationVal ? context.lineWidth : 0),
  };
};
