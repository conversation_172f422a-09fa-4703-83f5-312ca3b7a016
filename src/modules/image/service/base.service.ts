import { Provide, Inject, httpError } from '@midwayjs/core';
import { HttpService } from '@midwayjs/axios';
import sharp from 'sharp';
import fs from 'fs-extra';
import { URL } from 'node:url';
import { join } from 'node:path';
import { Canvas } from 'canvas';
import { consoleTime } from '@/core/utils/console-time.js';
import { streamArrayBufferToFile, getFileName } from './utils/canvas.js';
import { FONTS_TEMP_PATH, IMAGES_TEMP_PATH } from '@/constants/index.js';
import { ToFormatOpts, ImageFileType } from '../interface/image.interface.js';
import { LockfileService } from '@/modules/lockfile/service/lockfile.service.js';
import { OssService } from '@/modules/oss/service/oss.service.js';

@Provide()
export class BaseService {
  @Inject() private httpService: HttpService;

  @Inject() private lockfileService: LockfileService;

  @Inject() private ossService: OssService;

  /**
   * 下载文字字体
   * @param url 字体地址
   * @returns
   */
  async downloadFont(url: string) {
    const fileName = url.split('/').pop();
    const localPath = join(FONTS_TEMP_PATH, fileName);
    const isExist = await fs.exists(localPath);
    if (isExist) {
      return localPath;
    }
    const data = await this.ossService.downloadFile(fileName);
    await this.lockfileService.lockFile(
      () => {
        return streamArrayBufferToFile(data, localPath);
      },
      fileName,
      200
    );
    return localPath;
  }
  async writeLocalFile(url: string) {
    const [fileUrl, fileQuery = ''] = url.split('?');
    const fileName = fileUrl.split('/').pop();
    const filePath = [
      fileQuery && fileQuery.replace('x-oss-process=image', '').split('/'),
      fileName,
    ]
      .flat()
      .filter(Boolean)
      .join(':');

    const localPath = join(IMAGES_TEMP_PATH, filePath);
    const ret = {
      buffer: null as null | Buffer,
      writeFile: async (data: Buffer) => {
        await this.lockfileService.lockFile(() => {
          return fs.writeFile(localPath, data);
        }, filePath);
      },
    };
    const isExist = await fs.exists(localPath);
    if (isExist) {
      ret.buffer = await fs.readFile(localPath);
    }
    return ret;
  }
  async downloadImage(url: string, writeLocalFile = true) {
    let localData: null | Awaited<ReturnType<typeof this.writeLocalFile>>;
    if (writeLocalFile) {
      localData = await this.writeLocalFile(url);
    }
    if (localData?.buffer) {
      return Buffer.from(localData.buffer);
    }
    // const [filepath, fileQuery = ''] = url.split('?');
    // const fileName = filepath.split('/').pop();
    // 判断图片来源
    let data: ArrayBuffer;
    const urlData = new URL(url);
    if (urlData.hostname.includes('oss.yunbanfang.cn')) {
      const fileName = urlData.pathname.split('/').filter(Boolean).join('-');
      const fileQuery = urlData.searchParams.get('x-oss-process') || '';
      const result = await this.ossService.downloadFile(
        fileName,
        // fileQuery.split('=').pop()
        fileQuery
      );
      data = result;
    } else {
      const timeEnd = consoleTime('http下载文件');
      const result = await this.httpService.get(url, {
        responseType: 'arraybuffer',
      });
      timeEnd();
      data = result.data;
    }
    localData?.writeFile(Buffer.from(data));

    return Buffer.from(data);
  }
  /**
   * 上传canvas图片
   */
  async uploadCanvasImage(
    canvas: Canvas,
    fileType: ImageFileType
  ): Promise<string> {
    let fileBuffer: Buffer;

    if (fileType === 'jpeg') {
      fileBuffer = canvas.toBuffer('image/jpeg', {
        quality: 1,
        chromaSubsampling: false,
      });
    } else {
      fileBuffer = canvas.toBuffer('image/png', {
        filters: canvas.PNG_ALL_FILTERS,
        resolution: 300,
      });
    }
    const url = await this.ossService.uploadFile(
      `${getFileName()}.${fileType}`,
      fileBuffer
    );

    return url;
  }
  async uploadFileWithBuffer(
    fileBuffer: Buffer,
    fileType: ToFormatOpts['format']
  ): Promise<string> {
    const url = await this.ossService.uploadFile(
      `${getFileName()}.${fileType}`,
      fileBuffer
    );
    return url;
  }
  /**
   * 不是oss图片的，使用sharp下载来解析
   */
  async getImageMetaBySharp(url: string) {
    const image = sharp(await this.downloadImage(url));
    const metaData = await image.metadata();

    return {
      width: metaData.width,
      height: metaData.height,
    };
  }
  /**
   * 获取oss图片信息
   */
  async getOssImageData(url: string) {
    if (!url) {
      throw new httpError.BadRequestError('getOssImageData: url 必传');
    }
    const fileName = url.split('?').shift().split('/').pop();
    const uri = new URL(url);
    if (!uri?.hostname) {
      throw new httpError.BadRequestError(
        `getOssImageData: 图片地址有误 - ${url}`
      );
    }
    const isOssUrl = uri?.hostname?.includes('oss.yunbanfang.cn');

    if (!isOssUrl) {
      return await this.getImageMetaBySharp(url);
    }
    const data = await this.ossService.getImageInfo(fileName);
    const imageData = {
      width: Number(data.ImageWidth.value),
      height: Number(data.ImageHeight.value),
    };
    const match = url.match(/image\/resize,w_(\d+)/);
    if (match) {
      const w = Number(match[1]);
      // 如果实际图片比裁剪图片要小，就直接使用实际图片尺寸
      if (w >= imageData.width) {
        return imageData;
      }
      const h = (w * imageData.height) / imageData.width;
      imageData.width = w;
      imageData.height = h;
    }
    return imageData;
  }
}
