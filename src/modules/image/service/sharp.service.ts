import { Provide, Inject, httpError } from '@midwayjs/core';
import sharp from 'sharp';
import { join, extname } from 'node:path';
import fs from 'fs-extra';
import { BaseService } from './base.service.js';
import {
  ToFormatOpts,
  ImageExtract,
  UrlSizeResizeOpts,
  UrlSizeResizeRet,
} from '../interface/image.interface.js';
import { JSON_TEMP_PATH } from '@/constants/index.js';
import { getUrlSizeResizeCacheKey } from './utils/cache.js';
import { LockfileService } from '@/modules/lockfile/service/lockfile.service.js';
import { tryReadJson } from '@/core/utils/try-read-json.js';

@Provide()
export class sharpService {
  @Inject() private baseService: BaseService;

  @Inject() private lockfileService: LockfileService;

  /**
   * 使用 sharp 处理图片
   * 避免 node-canvas 图片处理导致图片被压缩质量
   */
  async handleGetImageBufferByWH(
    url: string | Buffer,
    width: number,
    height: number
  ) {
    const imageBuffer = await sharp(
      Buffer.isBuffer(url) ? url : await this.baseService.downloadImage(url)
    )
      .resize(Math.round(width), Math.round(height), {
        // 高质量缩放算法
        kernel: sharp.kernel.lanczos3,
      })
      .toBuffer();

    return imageBuffer;
  }
  /**
   * 图片转格式
   */
  async toFormat({ url, format }: ToFormatOpts) {
    const imageBuffer = await this.baseService.downloadImage(url);
    const buffer = await sharp(imageBuffer).toFormat(format).toBuffer();
    return await this.baseService.uploadFileWithBuffer(buffer, format);
  }
  /**
   * 图片裁剪
   */
  async imageExtract(opts: ImageExtract) {
    const { url, rect, format, imageFileTypeAutoToFormat = true } = opts;
    const getFormatFromUrl = (_url: string) => {
      const regex = /format,([^&]+)/;
      const match = regex.exec(_url);
      return match ? match[1] : 'jpg';
    };

    const isBuffer = Buffer.isBuffer(url);
    let image = sharp(
      isBuffer ? url : await this.baseService.downloadImage(url)
    );
    if (format) {
      image = image.toFormat(format);
    }
    const metadata = await image.metadata();
    // 超出边界直接报错，避免裁剪出错
    const isOverWidth = rect.left + rect.width > metadata.width;
    const isOverHeight = rect.top + rect.height > metadata.height;
    if (isOverWidth) {
      rect.width = metadata.width - rect.left;
    }
    if (isOverHeight) {
      rect.height = metadata.height - rect.top;
    }

    const buffer = await image.extract(rect).toBuffer();
    const f =
      // 有传 format 的直接使用
      format ||
      (() => {
        if (isBuffer) {
          return 'png';
        }
        const filePath = url.split('?')[0];
        // 假如是 ‘.image’ 格式，进行自动转换
        if (extname(filePath) === '.image' && imageFileTypeAutoToFormat) {
          return getFormatFromUrl(url) as ToFormatOpts['format'];
        }
        // 使用原本的格式
        const [, fileType] = filePath.split('/').pop().split('.');
        return fileType as ToFormatOpts['format'];
      })();
    return await this.baseService.uploadFileWithBuffer(buffer, f);
  }
  /**
   * 用于图片旋转
   */
  async imageRotate(url: string | Buffer, rotate: number) {
    const imageBuffer = sharp(
      Buffer.isBuffer(url) ? url : await this.baseService.downloadImage(url)
    )
      .toFormat('png')
      .rotate(Number(rotate) || 0, {
        background: {
          r: 0,
          g: 0,
          b: 0,
          alpha: 0,
        },
      });
    const newImageSharp = sharp(await imageBuffer.toBuffer());
    const metadata = await newImageSharp.metadata();

    return {
      width: metadata.width,
      height: metadata.height,
      buffer: await newImageSharp.toBuffer(),
    };
  }
  /**
   * 提取非透明区域并裁剪
   */
  async cropTransparentArea(url: string | Buffer) {
    let imageBuffer = Buffer.isBuffer(url)
      ? url
      : await this.baseService.downloadImage(url);
    const image = sharp(imageBuffer);
    const { width, height, format } = await image.metadata();
    if (format !== 'png') {
      throw new httpError.BadRequestError('非 png 格式图片不支持透明区域裁剪');
    }
    // 提取 Alpha 通道数据
    const alphaChannel = await image
      .clone()
      .ensureAlpha() // 确保有 Alpha 通道
      .extractChannel(3) // 提取 Alpha 通道
      .raw() // 提取原始数据
      .toBuffer();
    // 找到有内容的区域
    let top = height;
    let bottom = 0;
    let left = width;
    let right = 0;

    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const alphaValue = alphaChannel[y * width + x];
        if (alphaValue > 0) {
          // 非透明像素，更新边界
          top = Math.min(top, y);
          bottom = Math.max(bottom, y);
          left = Math.min(left, x);
          right = Math.max(right, x);
        }
      }
    }
    // 裁剪图像
    const cropWidth = right - left + 1;
    const cropHeight = bottom - top + 1;
    return await this.baseService.uploadFileWithBuffer(
      await image
        .extract({
          left,
          top,
          width: Math.min(width, cropWidth),
          height: Math.min(height, cropHeight),
        })
        .toBuffer(),
      'png'
    );
  }

  /**
   * 以底图的尺寸为基准，将蒙层图的尺寸调整为一致
   */
  async urlSizeResize(opts: UrlSizeResizeOpts) {
    const { url, maskUrl } = opts;
    // 先下载底图图片
    const imageBuffer = await this.baseService.downloadImage(url);
    const image = sharp(imageBuffer);
    const imageMetadata = await image.metadata();
    // 尝试读取缓存中的数据
    const { cacheKey } = getUrlSizeResizeCacheKey(
      maskUrl,
      imageMetadata.width,
      imageMetadata.height
    );
    // 本地地址
    const tmpJsonFile = join(JSON_TEMP_PATH, `${cacheKey}.json`);
    const cacheData = await tryReadJson<UrlSizeResizeRet>(tmpJsonFile);
    if (cacheData) return cacheData;
    // 没有缓存，直接处理
    const maskImageBuffer = await this.baseService.downloadImage(maskUrl);
    const maskImage = sharp(maskImageBuffer);
    const maskImageMetadata = await maskImage.metadata();
    let ret: UrlSizeResizeRet;
    // 图片尺寸不一致
    if (
      imageMetadata.width !== maskImageMetadata.width ||
      imageMetadata.height !== maskImageMetadata.height
    ) {
      const newMaskUrl = await this.baseService.uploadFileWithBuffer(
        await maskImage
          .clone()
          .resize({
            width: imageMetadata.width,
            height: imageMetadata.height,
            fit: sharp.fit.contain,
            background: '#000',
          })
          .toBuffer(),
        maskImageMetadata.format
      );

      ret = {
        url,
        maskUrl: newMaskUrl,
        width: imageMetadata.width,
        height: imageMetadata.height,
      };
    } else {
      ret = {
        url,
        maskUrl,
        width: imageMetadata.width,
        height: imageMetadata.height,
      };
    }
    // 写入缓存
    this.lockfileService.lockFile(
      () => fs.writeJSON(tmpJsonFile, ret),
      cacheKey
    );
    // TODO: 调用 getContoursRectWithCache

    return ret;
  }
}
