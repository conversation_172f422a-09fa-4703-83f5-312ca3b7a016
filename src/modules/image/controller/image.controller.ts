import { Inject, Controller, Post, Body } from '@midwayjs/core';
import { ImageService } from '../service/image.service.js';
import { controllerResponse } from '@/core/utils/index.js';
import { sharpService } from '../service/sharp.service.js';
import {
  ImageSynthesisFnOpts,
  CreateDecorativeImageWithMaskOpts,
  ToFormatOpts,
  ImageExtract,
  ClearTrajectoryPathOpts,
  GetContoursRectDataOpts,
  CreateContourImageOpts,
  UrlSizeResizeOpts,
} from '../interface/image.interface.js';
import { consoleTime } from '@/core/utils/console-time.js';

@Controller('/api/image')
export class APIController {
  @Inject() private imageService: ImageService;

  @Inject() private sharpService: sharpService;

  @Post('/create-decorative-image-with-mask')
  async createDecorativeImageWithMask(
    @Body() opts: CreateDecorativeImageWithMaskOpts
  ) {
    const res = await this.imageService.createDecorativeImageWithMask(opts);
    return controllerResponse(res.url);
  }

  @Post('/image-synthesis')
  async imageSynthesis(@Body() opts: ImageSynthesisFnOpts) {
    const timeEnd = consoleTime('生图时间');
    const res = await this.imageService.imageSynthesis(opts);
    timeEnd();
    return controllerResponse(res);
  }

  @Post('/to-format')
  async toFormat(@Body() opts: ToFormatOpts) {
    const res = await this.sharpService.toFormat(opts);
    return controllerResponse(res);
  }
  @Post('/image-extract')
  async imageExtract(@Body() opts: ImageExtract) {
    const res = await this.sharpService.imageExtract(opts);
    return controllerResponse(res);
  }

  @Post('/get-contours-rect-data')
  async getContoursRectData(@Body() opts: GetContoursRectDataOpts) {
    const res = await this.imageService.getContoursRectData(opts);
    return controllerResponse(res);
  }

  @Post('/clear-trajectory-path')
  async clearTrajectoryPath(@Body() opts: ClearTrajectoryPathOpts) {
    const res = await this.imageService.clearTrajectoryPath(opts);
    return controllerResponse(res);
  }

  @Post('/create-contour-image')
  async createContourImage(@Body() opts: CreateContourImageOpts) {
    const res = await this.imageService.createContourImage(opts);
    return controllerResponse(res);
  }

  @Post('/crop-transparent-area')
  async cropTransparentArea(@Body() opts: { url: string }) {
    return controllerResponse(
      await this.sharpService.cropTransparentArea(opts.url)
    );
  }
  @Post('/url-size-resize')
  async urlSizeResize(@Body() opts: UrlSizeResizeOpts) {
    return controllerResponse(await this.sharpService.urlSizeResize(opts));
  }
}
