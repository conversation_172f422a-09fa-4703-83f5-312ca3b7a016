import { Inject, Controller, Post, Body } from '@midwayjs/core';
import { ImageService } from '../service/image.service.js';
import { controllerResponse } from '@/core/utils/index.js';
import { CreateDecorativeImageWithMaskOpts } from '../interface/image.interface.js';

@Controller('/api/v2/image')
export class APIController {
  @Inject() private imageService: ImageService;

  @Post('/create-decorative-image-with-mask')
  async v2CreateDecorativeImageWithMask(
    @Body() opts: CreateDecorativeImageWithMaskOpts
  ) {
    const res = await this.imageService.createDecorativeImageWithMask(opts);
    return controllerResponse(res);
  }
}
