# robot-sew-node-service

## 快速入门

<!-- 在此次添加使用文档 -->

如需进一步了解，参见 [midway 文档][midway]。

### 环境依赖

- Node.js 22+
- node-canvas 的 `cairo` 等依赖 [查看安装方式](https://github.com/Automattic/node-canvas?tab=readme-ov-file#compiling)
- sharp.js 的 `libvips` 依赖，一般情况下会自行安装，如缺失，参考下面的安装方式

```bash
// Ubuntu/Debian
$ apt update
$ apt install -y libvips libvips-dev

// CentOS/RHEL
$ yum install -y epel-release
$ yum install -y vips vips-devel

// macOS
$ brew install vips

// Windows
@see: https://github.com/libvips/libvips/releases
```

### 本地开发

```bash
$ pnpm i
$ pnpm run dev
$ open http://localhost:7001/
```

### 部署

```bash
$ pnpm start
```

### 内置指令

- 使用 `npm run lint` 来做代码风格检查。
- 使用 `npm test` 来执行单元测试。

[midway]: https://midwayjs.org
