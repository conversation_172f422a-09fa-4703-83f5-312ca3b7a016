{"name": "robot-sew-node-service", "version": "1.0.0", "description": "", "private": true, "type": "module", "dependencies": {"@midwayjs/axios": "^3.19.1", "@midwayjs/bootstrap": "^3.12.0", "@midwayjs/bull": "^3.0.0", "@midwayjs/bull-board": "^3.0.0", "@midwayjs/cache": "^3.14.0", "@midwayjs/core": "^3.12.0", "@midwayjs/cross-domain": "^3.19.2", "@midwayjs/info": "^3.12.0", "@midwayjs/jwt": "^3.0.0", "@midwayjs/koa": "^3.12.0", "@midwayjs/logger": "^3.1.0", "@midwayjs/oss": "^3.0.0", "@midwayjs/redis": "^3.19.0", "@midwayjs/security": "^3.19.0", "@midwayjs/static-file": "^3.19.0", "@midwayjs/validate": "^3.12.0", "@thednp/dommatrix": "^2.0.10", "cache-manager": "^6.3.0", "canvas": "^2.11.2", "form-data": "^4.0.1", "fs-extra": "^11.2.0", "piscina": "^4.7.0", "rate-limiter-flexible": "^5.0.4", "sharp": "^0.33.5"}, "devDependencies": {"@midwayjs/luckyeye": "^1.1.0", "@midwayjs/mock": "^3.12.0", "@types/cache-manager": "^4.0.6", "@types/fs-extra": "^11.0.4", "@types/mocha": "^10.0.1", "@types/node": "20", "c8": "^8.0.1", "cross-env": "^7.0.3", "mocha": "^10.2.0", "mwts": "^1.3.0", "mwtsc": "^1.4.0", "ts-node": "^10.9.2", "typescript": "~5.1.0"}, "scripts": {"start:dev": "cross-env NODE_ENV=production API_ENV=dev1 pm2-runtime ./bootstrap.js --name robot-sew-node-service -i 4", "start:qa": "cross-env NODE_ENV=production API_ENV=qa1 pm2-runtime ./bootstrap.js --name robot-sew-node-service -i 4", "start:uat": "cross-env NODE_ENV=production API_ENV=uat1 pm2-runtime ./bootstrap.js --name robot-sew-node-service -i 4", "start:blue": "cross-env NODE_ENV=production API_ENV=blue pm2-runtime ./bootstrap.js --name robot-sew-node-service -i 4", "start:prod": "cross-env NODE_ENV=production API_ENV=prod pm2-runtime ./bootstrap.js --name robot-sew-node-service -i 4", "dev": "cross-env NODE_ENV=local API_ENV=dev1 mwtsc --watch --run @midwayjs/mock/app", "test": "cross-env NODE_ENV=unittest mocha", "cov": "cross-env c8 --all --reporter=text --reporter=lcovonly npm run test", "lint": "mwts check", "lint:fix": "mwts fix", "ci": "npm run cov", "build": "mwtsc --cleanOutDir", "check": "luckyeye"}, "repository": {"type": "git", "url": ""}, "author": "anonymous", "midway-luckyeye": {"packages": ["midway_v2"]}, "engines": {"node": ">=22.0.0", "pnpm": ">=9.0.0"}, "packageManager": "pnpm@9.0.0"}