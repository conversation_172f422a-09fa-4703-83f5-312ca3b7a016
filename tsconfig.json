{
  "compileOnSave": true,
  "compilerOptions": {
    "baseUrl": "./",
    "target": "ESNext",
    "module": "NodeNext",
    "moduleResolution": "NodeNext",
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "inlineSourceMap":true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "stripInternal": true,
    "skipLibCheck": true,
    "pretty": true,
    "declaration": true,
    "forceConsistentCasingInFileNames": true,
    "paths": {
      "@/*": ["src/*"],
      "@controllers/*": ["src/controllers/*"],
      "@services/*": ["src/services/*"],
    },
    "typeRoots": [ "./typings", "./node_modules/@types"],
    "outDir": "dist",
    "rootDir": "src"
  },
  "exclude": [
    "*.js",
    "*.ts",
    "dist",
    "node_modules",
    "test"
  ]
}
